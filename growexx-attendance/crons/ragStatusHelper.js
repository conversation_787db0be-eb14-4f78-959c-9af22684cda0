/* eslint-disable no-unused-vars */
/* eslint-disable no-console */
/* eslint-disable max-len */
const axios = require('axios');
const moment = require('moment');
const constants = require('../server/util/constants');
const Logger = require('../server/util/logger');
const Rag = require('../server/models/rag.model');
const Portal = require('../server/models/portal.model');
const projectBoardModel = require('../server/models/projectBoard.model');
const getSprintMetrics = require('./sprintMetricsReportHelper');
const PtCronStatus = require('../server/models/ptCronStatus.model');
const { default: mongoose } = require('mongoose');

/**
 * Fetches data from a Jira API.
 *
 * @param {string} jiraUrl - The URL of the Jira API.
 * @param {object} portal - The portal object containing email and token.
 * @param {boolean} [isPaginate=true] - Whether to paginate the results.
 * @return {Promise<Array|null>} - A promise that resolves to an array of data or null.
 */
const fetchJiraData = async (jiraUrl, portal, isPaginate = true) => {
    const responseData = [];
    let isLast = false;
    let startAt = 0;
    try {
        do {
            const url = isPaginate ? `${jiraUrl}?startAt=${startAt}` : jiraUrl;

            const response = await axios.get(url, {
                headers: {
                    Authorization: `Basic ${Buffer.from(
                        `${portal.email}:${portal.token}`
                    ).toString('base64')}`,
                    Accept: 'application/json',
                    'Content-Type': 'application/json'
                }
            });
            if (!isPaginate) {
                return response.data;
            }
            responseData.push(...response.data.values);

            isLast = response.data.isLast;

            startAt += response.data.maxResults;
        } while (!isLast);
        return responseData;
    } catch (error) {
        return null;
    }
};
/**
 * Finds the nearest closed sprint from the given sprint array based on the provided date.
 * @param {Array} sprintArray - An array of sprint objects.
 * @param {Date} date - The date to compare with sprint end dates.
 * @return {Object|null} The nearest closed sprint object, or null if no closed sprint is found.
 */
function findNearestClosedSprint (sprintArray, date) {
    if (sprintArray.length === 0) {
        return null;
    }
    const closedSprints = sprintArray.filter(
        (sprint) => sprint.state === 'closed'
    );

    if (closedSprints.length === 0) {
        return null;
    }

    const nearestSprint = closedSprints.reduce((nearest, sprint) => {
        const endDate = new Date(sprint.endDate);
        const timeDifference = Math.abs(endDate - date);

        if (
            !nearest ||
            (timeDifference < nearest.timeDifference && endDate <= date)
        ) {
            return { sprint, timeDifference };
        } else {
            return nearest;
        }
    }, null);

    return nearestSprint.sprint;
}
/**
 * Returns the flag corresponding to the given value based on the provided ranges.
 *
 * @param {number} value - The value to be checked against the ranges.
 * @param {Array<Object>} ranges - An array of objects containing min, max, and flag properties.
 * @return {number} The flag corresponding to the value, or 0 if no match is found.
 */
const getFlag = (value, ranges) => {
    for (let i = 0; i < ranges.length; i++) {
        if (value >= ranges[i].min && value < ranges[i].max) {
            return ranges[i].flag;
        }
    }
    return 0;
};

/**
 * Calculates the color code for the given data.
 *
 * @param {Object} data - Object containing openCloseRatio, effortVariance, mitigatedBugCount, and pendingBugCount.
 * @return {Object} - Object containing the color codes for openClose, ev, and b2d, and the overall color code.
 */
const calcColorCode = (data) => {
    const {
        openCloseRatio,
        effortVariance,
        mitigatedBugCount,
        countOfNonLegacyBugsSolvedInSprint,
        pendingBugCount,
        pendingNonLegacyBugCount

    } = data;

    // Calculate the flag for openClose based on the ratio
    const openCloseFlag = getFlag(openCloseRatio, [
        { min: 75, max: 100, flag: 2 },
        { min: 55, max: 75, flag: 1 }
    ]);

    // Calculate the flag for effortVariance based on the percentage
    const effortVariancePercentage = effortVariance * 100;
    const effortVarianceFlag = getFlag(effortVariancePercentage, [
        { min: Number.NEGATIVE_INFINITY, max: 10, flag: 2 },
        { min: 10, max: 20, flag: 1 }
    ]);

    // Calculate the flag for bug based on the count
    let bugFlag = getFlag(countOfNonLegacyBugsSolvedInSprint, [
        { min: 1, max: 3, flag: 2 },
        { min: 3, max: 5, flag: 1 }
    ]);

    bugFlag = pendingNonLegacyBugCount ? 0 : bugFlag;

    const flags = [openCloseFlag, effortVarianceFlag, bugFlag];

    const color = flags.includes(0)
        ? constants.COLOR_CODE.RED
        : flags.includes(1)
            ? constants.COLOR_CODE.AMBER
            : constants.COLOR_CODE.GREEN;

    return {
        openClose:
            openCloseFlag === 0
                ? constants.COLOR_CODE.RED
                : openCloseFlag === 1
                    ? constants.COLOR_CODE.AMBER
                    : openCloseFlag === 2
                        ? constants.COLOR_CODE.GREEN
                        : '',
        ev:
            effortVarianceFlag === 0
                ? constants.COLOR_CODE.RED
                : effortVarianceFlag === 1
                    ? constants.COLOR_CODE.AMBER
                    : effortVarianceFlag === 2
                        ? constants.COLOR_CODE.GREEN
                        : '',
        b2d:
            bugFlag === 0
                ? constants.COLOR_CODE.RED
                : bugFlag === 1
                    ? constants.COLOR_CODE.AMBER
                    : bugFlag === 2
                        ? constants.COLOR_CODE.GREEN
                        : '',
        overall: color
    };
};

/**
 * Calculates the RAG data for the given sprint
 *
 * @param {Object} sprint - The sprint object
 * @param {string} boardId - The board ID
 * @param {Array} data - The array of issues
 * @param {Object} portal - The portal object
 * @returns {Object} The RAG data object
 */
const calculateRAGData = async (sprint, boardId, data, portal) => {
    const sprintStartDate = new Date(sprint.startDate);
    const sprintEndDate = new Date(sprint.completeDate);
    const bugCount = new Map();
    const solvedBugCount = new Map();
    const teamMembers = {};
    let doneStoryCount = 0;
    let totalStoryCount = 0;
    let bugsCreatedDuringSprint = 0;
    let countOfBugsSolvedInSprint = 0;
    let countOfNonLegacyBugsSolvedInSprint = 0;
    let spillOverBugs = 0;
    let totalPlannedEfforts = 0;
    let totalSpentEfforts = 0;
    const individualEffortHours = {};
    const storySubtaskMap = new Map();

    let rejectedNonLegacyBugs = 0;

    data.forEach(issue => {
        // Track team members based on issue label
        issue.fields.labels?.forEach(label => {
            teamMembers[label] = (teamMembers[label] || 0) + 1;
        });
        const issueDate = new Date(issue.fields.resolutiondate);
        const issueCreatedDate = new Date(issue.fields.created);
        switch (issue.fields.issuetype.name) {
            case constants.ISSUE_TYPE.BUG:{
                if (sprintStartDate <= issueCreatedDate && sprintEndDate >= issueCreatedDate) {
                    bugsCreatedDuringSprint += 1;
                }
                if (sprintStartDate <= issueCreatedDate && sprintEndDate >= issueCreatedDate && issue.fields.status.name !== 'Done') {
                    spillOverBugs += 1;
                }
                // Track bugs solved during the sprint
                if (issue.fields.status.name === 'Done' && sprintStartDate <= issueDate && sprintEndDate >= issueDate) {
                    countOfBugsSolvedInSprint += 1;
                    if (!issue.fields.labels.includes('legacy')) {
                        console.log('non-legacy-solved-bug', issue.key);
                        countOfNonLegacyBugsSolvedInSprint += 1;
                    }
                } else if (!issue.fields.labels.includes('legacy')) {
                    console.log('non-legacy-rejected-bug', issue.key);
                    rejectedNonLegacyBugs++;
                }
                // Count bugs per developer based on labels
                issue.fields.labels?.forEach(label => {
                    bugCount.set(label, (bugCount.get(label) || 0) + 1);
                    // Bugs Solved during Sprint by each developer
                    if (issue.fields.status.name === 'Done' && sprintStartDate <= issueDate && sprintEndDate >= issueDate) {
                        solvedBugCount.set(label, (solvedBugCount.get(label) || 0) + 1);
                    }
                });
                const isBugDoneInCurrentSprint = issue.fields.status.name === 'Done' && sprintStartDate <= issueDate && sprintEndDate >= issueDate;
                storySubtaskMap.set(issue.key, {
                    isStoryDoneInCurrentSprint: isBugDoneInCurrentSprint,
                    originalEstimate: issue.fields.timetracking.originalEstimateSeconds || 0,
                    subtasks: []
                });
                break;
            }

            case constants.ISSUE_TYPE.STORY:{
                totalStoryCount += 1;

                const isStoryDoneInCurrentSprint = issue.fields.status.name === 'Done' && sprintStartDate <= issueDate && sprintEndDate >= issueDate;
                if (isStoryDoneInCurrentSprint) {
                    doneStoryCount += 1;
                }

                storySubtaskMap.set(issue.key, {
                    isStoryDoneInCurrentSprint,
                    originalEstimate: issue.fields.timetracking.originalEstimateSeconds || 0,
                    subtasks: []
                });
                break;
            }

            case constants.ISSUE_TYPE.SUBTASK:{
                const parentKey = issue.fields.parent.key;
                if (storySubtaskMap.has(parentKey)) {
                    const parentStory = storySubtaskMap.get(parentKey);
                    parentStory.subtasks.push(issue);
                }
                break;
            }

            default:
                break;
        }
    });

    // Process subtasks based on the status of their parent story
    storySubtaskMap.forEach((storyData, storyKey) => {
        const { isStoryDoneInCurrentSprint, originalEstimate, subtasks } = storyData;
        if (isStoryDoneInCurrentSprint) {
            // Add the original estimate from the parent story once
            totalPlannedEfforts += originalEstimate;

            subtasks.forEach(subtask => {
                const plannedEffort = subtask.fields.timetracking.originalEstimateSeconds || 0;
                const spentEffort = subtask.fields.timetracking.timeSpentSeconds || 0;

                // console.log(`Subtask Key: ${subtask.key}, original time: ${originalEstimate / 3600} sub task time: ${subtask.fields.timetracking.originalEstimateSeconds / 3600} Spent Time: ${subtask.fields.timetracking.timeSpentSeconds / 3600} hours`);
                totalSpentEfforts += spentEffort;

                // Track individual efforts by developer for time spent
                subtask.fields.labels?.forEach(label => {
                    if (!individualEffortHours[label]) {
                        individualEffortHours[label] = { plannedEfforts: 0, spentHours: 0, effortVariance: 0 };
                    }
                    individualEffortHours[label].plannedEfforts += plannedEffort;
                    individualEffortHours[label].spentHours += spentEffort;

                    // Calculate effort variance for individual labels
                    individualEffortHours[label].effortVariance = (
                        ((individualEffortHours[label].spentHours - individualEffortHours[label].plannedEfforts) / (individualEffortHours[label].plannedEfforts || 1)) * 100
                    ).toFixed(2);
                });
            });
        }
    });

    // Convert total efforts from seconds to hours
    const totalPlannedEffortsInHours = totalPlannedEfforts / 3600;
    const totalSpentEffortsInHours = totalSpentEfforts / 3600;
    const developerWiseBugsCount = Object.fromEntries(bugCount.entries());
    const totalBugCountAssignedToDeveloper = [...bugCount.values()].reduce((acc, val) => acc + val, 0);
    const developerWiseSolvedBugCount = Object.fromEntries(solvedBugCount.entries());

    const {
        doneIssuesName
    } = await getSprintReport(sprint.id, boardId, portal);

    const completedStoriesCount = doneStoryCount;
    const totalPlannedStories = totalStoryCount;
    const pendingNonLegacyBugCount = rejectedNonLegacyBugs;
    console.log('pendingNonLegacyBugCount', pendingNonLegacyBugCount);
    console.log('countOfNonLegacyBugsSolvedInSprint', countOfNonLegacyBugsSolvedInSprint);

    const openCloseRatio = ((completedStoriesCount / totalPlannedStories) * 100).toFixed(2);

    // Calculate effort variance using converted values in hours
    const effortVariance = (totalSpentEffortsInHours - totalPlannedEffortsInHours) / (totalPlannedEffortsInHours || 1);

    // Logger.debug({
    //     totalPlannedStories,
    //     doneStoryCount: completedStoriesCount + completedInAnotherSprintStoriesCount,
    //     totalPlannedEfforts: totalPlannedEffortsInHours,
    //     totalSpentEfforts: totalSpentEffortsInHours
    // });

    // const colorCode = calcColorCode({
    //     developerWiseBugsCount,
    //     totalBugCountAssignedToDeveloper,
    //     openCloseRatio,
    //     countOfBugsSolvedInSprint,
    //     effortVariance,
    //     mitigatedBugCount,
    //     countOfNonLegacyBugsSolvedInSprint,
    //     pendingBugCount,
    //     pendingNonLegacyBugCount
    // });
    return {
        labels: Object.keys(teamMembers),
        developerWiseBugsCount,
        developerWiseSolvedBugCount,
        totalBugCountAssignedToDeveloper,
        openCloseRatio,
        totalPlannedEfforts: totalPlannedEffortsInHours,
        totalSpentEfforts: totalSpentEffortsInHours,
        effortVariance: (effortVariance * 100).toFixed(2),
        totalBugs: countOfNonLegacyBugsSolvedInSprint + pendingNonLegacyBugCount,
        countOfBugsSolvedInSprint,
        countOfNonLegacyBugsSolvedInSprint,
        bugsCreatedDuringSprint,
        spillOverBugs,
        completedStoriesCount,
        totalPlannedStories,
        individualEffortHours,
        doneIssuesName
    };
};


/**
 * Gets the sprint report for the given sprint and board
 * @param {string} sprintId - The ID of the sprint
 * @param {string} boardId - The ID of the board
 * @param {Object} portal - The portal object
 * @returns {Object} The sprint report object
 */
const getSprintReport = async (sprintId, boardId, portal) => {
    const reportUrl = `https://${portal.url}/rest/greenhopper/1.0/rapid/charts/sprintreport?rapidViewId=${boardId}&sprintId=${sprintId}`; // Find a replacement for this.
    const reportResponse = await axios.get(reportUrl, {
        headers: {
            Authorization: `Basic ${Buffer.from(
                `${portal.email}:${portal.token}`
            ).toString('base64')}`,
            Accept: 'application/json',
            'Content-Type': 'application/json'
        }
    });

    const {
        completedIssues
    } = reportResponse.data.contents;

    const doneIssuesName = getCompletedStoriesName(completedIssues);

    return {
        doneIssuesName
    };
};


const getCompletedStoriesName = (array) => {
    return array
        .filter(item => item.typeName === 'Story' || item.typeName === 'Bug')
        .map(item => item.key);
};


/**
 * Function to get the sprint metrics for the given sprint
 * @param {Object} sprint - The sprint object
 * @param {Object} portal - The portal object
 * @param {string} boardId - The board ID
 * @param {Array} boardData - The array of board data
 * @param {string} projectKey - The project key
 * @param {number} index - The index of the board in the array
 * @returns {Promise<void>}
 */
const getSprintMetricForGivenSprint = async (sprint, portal, boardId, boardData, projectKey, index) => {
    // Get the sprint issue data from Jira
    if (sprint) {
        const sprintIssueDataURL = `https://${portal.url}/rest/agile/1.0/sprint/${sprint.id}/issue`;
        const jiraSprintIssueData = [];
        let isLast = false;
        let startAt = 0;
        try {
            do {
                const url = `${sprintIssueDataURL}?startAt=${startAt}`;
                const response = await axios.get(url, {
                    headers: {
                        Authorization: `Basic ${Buffer.from(
                            `${portal.email}:${portal.token}`
                        ).toString('base64')}`,
                        Accept: 'application/json',
                        'Content-Type': 'application/json'
                    }
                });
                jiraSprintIssueData.push(...response.data.issues);
                if (
                    response.data.startAt + response.data.maxResults >=
                    response.data.total
                ) {
                    isLast = true;
                }
                startAt += response.data.maxResults;
            } while (!isLast);
        } catch (error) {
            console.info('Jira connection', error);
        }
        // Calculate the RAG data for the sprint
        const sprintUrl = `https://${portal.url}/jira/software/c/projects/${projectKey}/boards/${boardId}/reports/sprint-retrospective?sprint=${sprint.id}`;
        const data = await calculateRAGData(
            sprint,
            boardId,
            jiraSprintIssueData,
            portal
        );
        const team = {};
        data.labels.forEach((d) => {
            team[d] = 0;
        });
        Object.keys(data.developerWiseBugsCount).map((k) => {
            team[k] = { developerWiseTotalBugs: data.developerWiseBugsCount[k] || 0 };
        });
        Object.keys(data.developerWiseSolvedBugCount).map((k) => {
            team[k] = { ...team[k], developerWiseTotalSolvedBug: data.developerWiseSolvedBugCount[k] || 0 };
        });

        const unlabelledBugsCountJQL = `https://${portal.url}/jira/software/c/projects/${projectKey}/issues/?jql=project=${projectKey} AND type=Bug AND sprint=${sprint.id} AND labels is EMPTY`;
        const unlabelledBugsUrl = `https://${portal.url}/rest/api/2/search?jql=project=${projectKey} AND type=Bug AND sprint=${sprint.id} AND labels is EMPTY`;
        const bugData = await fetchJiraData(unlabelledBugsUrl, portal, false);
        await Rag.findOneAndUpdate(
            {
                $and: [
                    { project: boardData[index].project.projectName },
                    { boardId },
                    { jiraSprintId: sprint.id }
                ]
            },
            {
                $set: {
                    project: boardData[index].project.projectName,
                    sprintReport: sprintUrl,
                    sprintNumber: sprint.name,
                    team: Object.keys(team).map((t) => {
                        const individualEfforts = data.individualEffortHours[t] || {};
                        return {
                            member: t,
                            b2dCount: team[t].developerWiseTotalBugs || 0,
                            solvedBugCount: team[t].developerWiseTotalSolvedBug || 0,
                            plannedEfforts: Number(individualEfforts.plannedEfforts ?? 0),
                            spentHours: Number(individualEfforts.spentHours ?? 0),
                            effortVariance: Number(individualEfforts.effortVariance ?? 0)
                        };
                    }),
                    unlabelledBugsCount: bugData?.total,
                    openCloseRatio: Number(data.openCloseRatio) || 0,
                    effortVariance: Number(isNaN(data.effortVariance) ? 0 : data.effortVariance),
                    totalBugs: Number(data.totalBugs),
                    bugsCreatedDuringSprint: Number(data.bugsCreatedDuringSprint),
                    spillOverBugs: Number(data.spillOverBugs),
                    resolvedBugs: Number(data.countOfNonLegacyBugsSolvedInSprint),
                    sprintStart: new Date(sprint.startDate), sprintEnd: new Date(sprint.completeDate),
                    unlabelledBugsCountJQL,
                    boardId,
                    boardKey: projectKey,
                    jiraSprintId: sprint.id,
                    totalPlannedEfforts: Number(data.totalPlannedEfforts),
                    totalSpentEfforts: Number(data.totalSpentEfforts),
                    totalPlannedStories: Number(data.totalPlannedStories),
                    completedStoriesCount: Number(data.completedStoriesCount),
                    doneIssues: data.doneIssuesName || [],
                    updatedAt: new Date()
                },
                $setOnInsert: {
                    freeze: false,
                    createdAt: new Date()
                }
            },
            {
                upsert: true,
                new: true
            }
        );
    }
};

/**
 * @description This function is used to calculate and save RAG status of the sprint.
 * @param {string} syncAllData Flag to sync all data or only the latest sprint
 * @param {boolean} syncSprintMetricsReport Flag to sync sprint metrics report or not
 * @param {string} projectId Project ID
 * @returns {Promise<void>}
 */
const getRagStatus = async (syncAllData, syncSprintMetricsReport, projectId) => {
    try {
        /* we are not considering parkstreet projects as it can take long duration to fetch data */
        const parkstreetPortalId = mongoose.Types.ObjectId('60704d1eb965020716a63ab0');
        const projectParams = {
            portalId: { $ne: parkstreetPortalId }
        };

        if (projectId) {
            projectParams.projectId = mongoose.Types.ObjectId(projectId);
        }

        await PtCronStatus.findOneAndUpdate({}, { isRagCronRunning: true });

        const aggregateParams = [
            {
                $match: { ...projectParams }
            },
            {
                $lookup: {
                    from: 'projects',
                    localField: 'projectId',
                    foreignField: '_id',
                    as: 'project'
                }
            },
            {
                $lookup: {
                    from: 'sprints',
                    localField: '_id',
                    foreignField: 'boardId',
                    as: 'sprint'
                }
            },
            {
                $unwind: {
                    path: '$project'
                }
            },
            {
                $unwind: {
                    path: '$sprint'
                }
            },
            {
                $sort: {
                    'sprint.endDate': -1
                }
            },
            {
                $group: {
                    _id: '$jiraBoardName',
                    latestSprintEndDate: { $first: '$sprint.endDate' },
                    projectBoard: { $first: '$$ROOT' } // Store the whole document for reference
                }
            },
            {
                $replaceRoot: {
                    newRoot: {
                        $mergeObjects: [
                            '$projectBoard',
                            { latestSprintEndDate: '$latestSprintEndDate' }
                        ]
                    }
                }
            }
        ];

        const boardData = await projectBoardModel.aggregate(aggregateParams);

        for (let i = 0; i < boardData.length; i++) {
            const projectName = boardData[i].project.projectName;
            console.log('crone running for project ', projectName);
            const projectKey = boardData[i].jiraBoardName.split(' ')[0];
            const startDate = moment(Date.now()).format('YYYY-MM-DD');
            const boardId = boardData[i].jiraBoardId;
            const portal = await Portal.findById(boardData[i].portalId);
            const jiraSprintURL = `https://${portal.url}/rest/agile/1.0/board/${boardId}/sprint`;
            const SprintData = await fetchJiraData(jiraSprintURL, portal);
            if (SprintData) {
                if (syncAllData === 'all') {
                    for (let j = 0; j < SprintData.length; j++) {
                        const sprint = SprintData[j];
                        if (sprint.state === 'closed') {
                            console.log('crone running for sprint ', sprint.name);
                            await getSprintMetricForGivenSprint(sprint, portal, boardId, boardData, projectKey, i);
                        }
                    }
                } else {
                    const sprint = findNearestClosedSprint(
                        SprintData,
                        new Date(startDate)
                    );
                    await getSprintMetricForGivenSprint(sprint, portal, boardId, boardData, projectKey, i);
                }
            }
            if (syncSprintMetricsReport) {
                await getSprintMetrics('all', projectName);
            }
        }

        await PtCronStatus.findOneAndUpdate({}, { isRagCronRunning: false });
    } catch (error) {
        await PtCronStatus.findOneAndUpdate({}, { isRagCronRunning: false });
        throw error;
    }
};

module.exports = getRagStatus;
