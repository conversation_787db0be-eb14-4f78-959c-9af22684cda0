/**
 * Migration to add emails to exclusions list
 */

const emailsToExclude = [
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>',
    '<EMAIL>'
];

module.exports = {
    async up (db) {
        console.log('Running email exclusions migration...');

        const emailExclusions = emailsToExclude.map(email => ({
            email: email.toLowerCase(),
            createdAt: new Date()
        }));

        // Insert only emails that don't already exist
        for (const exclusion of emailExclusions) {
            const exists = await db.collection('excludedemails').findOne({ email: exclusion.email });
            if (!exists) {
                await db.collection('excludedemails').insertOne(exclusion);
                console.log(`Added email: ${exclusion.email}`);
            } else {
                console.log(`Email already exists: ${exclusion.email}`);
            }
        }

        console.log('Migration completed successfully');
    },

    async down (db) {
        console.log('Rolling back email exclusions update');
        // Optional: Add rollback logic if needed
    }
};
