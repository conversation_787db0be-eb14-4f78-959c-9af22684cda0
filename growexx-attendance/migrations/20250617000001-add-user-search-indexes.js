/* eslint-disable no-console */
/* eslint-disable no-unused-vars */
/* eslint-disable no-console */

module.exports = {
    async up (db, client) {
        try {
            console.log('Creating indexes...');


            await db.collection('users').createIndex(
                {
                    firstName: 'text',
                    lastName: 'text',
                    email: 'text',
                    employeeId: 'text'
                },
                {
                    name: 'user_search_index',
                    weights: {
                        firstName: 3,
                        lastName: 3,
                        email: 2,
                        employeeId: 1
                    }
                }
            );

            console.log('Text index created');

            await db.collection('users').createIndex({ role: 1 });
            await db.collection('users').createIndex({ isActive: 1 });
            console.log('Single field indexes created');

            console.log('All indexes created successfully');
        } catch (error) {
            console.error('Error creating indexes:', error.message);
            throw error;
        }
    },

    async down (db, client) {
        try {
            console.log('Dropping indexes...');


            await db.collection('users').dropIndex('user_search_index');


            await db.collection('users').dropIndex('role_1');
            await db.collection('users').dropIndex('isActive_1');

            console.log('All indexes dropped successfully');
        } catch (error) {
            console.error('Error dropping indexes:', error.message);
            throw error;
        }
    }
};
