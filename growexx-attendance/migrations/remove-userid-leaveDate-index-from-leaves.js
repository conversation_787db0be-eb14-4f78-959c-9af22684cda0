module.exports = {
    up: async (db) => {
      try {

        /** Check if the 'leaves' collection exists */
        const collections = await db.listCollections().toArray();
        const collectionExists = collections.some(col => col.name === 'leaves');

        /** If 'Leave' model exist, delete old index. */
        if (collectionExists) {
          /** Check existing indexes */
          const indexes = await db.collection('leaves').indexes();

          /** Drop the old index if it exists */
          const oldIndex = indexes.find(index => JSON.stringify(index.key) === JSON.stringify({ "userId": 1, "leaveDate": 1 }));
          if (oldIndex) {
            await db.collection('leaves').dropIndex({ "userId": 1, "leaveDate": 1 });
            console.log("Old index dropped successfully.");
          } else {
            console.log("Old index not found, skipping drop.");
          }

          /** Create a new compound index with 'userId', 'leaveDate', and 'leaveType' */
          await db.collection('leaves').createIndex(
            { "userId": 1, "leaveDate": 1, "leaveType": 1 },
            { unique: true }
          );

          console.log('Index updated successfully!');
        } else {
          console.log("'Leave' model not found, skipping create index.")
        }
      } catch (err) {
        console.error('Error updating index:', err);
        /** This will ensure the migration fails if there's an error */
        throw err;
      }
    },
   
    down: async (db) => {
      try {
        /** Drop the new index if you want to rollback the migration */
        await db.collection('leaves').dropIndex({ "userId": 1, "leaveDate": 1, "leaveType": 1 });
   
        /** Recreate the old unique index */
        await db.collection('leaves').createIndex(
          { "userId": 1, "leaveDate": 1 },
          { unique: true }
        );
   
        console.log('Index rolled back successfully!');
      } catch (err) {
        console.error('Error rolling back index:', err);
        throw err;
      }
    }
  };