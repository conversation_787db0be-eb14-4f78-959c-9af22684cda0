const validation = require('../../util/validation');

/**
 * Class represents validations for KRA.
 */
class AddKraValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate add a KRA
     * <AUTHOR>
     * @since 07/07/2022
     */
    async validationAddKra () {
        const { category, outcome, measure, target, designation, quarter, year } = this.body;
        super.category(category, 'category');
        super.outcome(outcome, 'outcome');
        super.measure(measure, 'measure');
        super.target(target, 'target');
        await super.designation(designation, 'designation');
        super.quarter(quarter, 'quarter');
        super.year(year, 'year');
    }
}
module.exports = AddKraValidator;
