const Project = require('../../models/project.model');
const Logs = require('../../models/logs.model');
const BusinessUnit = require('../../models/bu.model');
const mongoose = require('mongoose');
/**
 * Class represents services for get list of users.
 */
class ListProjectService {

    /**
     * @desc This function is being used to get list of userss
     * <AUTHOR>
     * @since 31/03/2021
     * @param {Object} req Request
     * @return {Object} response Success response
     */
    static async listProjects (req, user) {
        const options = {
            select: { projectName: 1, isActive: 1 },
            page: (req.query.page) ? req.query.page : 1,
            limit: (req.query.limit) ? req.query.limit : 10
        };
        const getCurrentQuarterYear = this.getCurrentQuarterYear();
        const quarter = req.query.quarter ? req.query.quarter : getCurrentQuarterYear.quarter;
        const year = req.query.year ? req.query.year : getCurrentQuarterYear.year;
        const aggregateParams = [];
        const whereCondition = await this.getQueryParam(req, user);
        const getQuarterDates = await this.getQuarterDates(quarter, year);
        const startDate = req.query.startDate ? MOMENT(req.query.startDate).format('YYYY-MM-DD') : getQuarterDates.firstDate;
        const endDate = req.query.endDate ? MOMENT(req.query.endDate).format('YYYY-MM-DD') : getQuarterDates.lastDate;
        const isBulkKra = !!req.query.isBulkKRA;
        aggregateParams.push(...this.getStaticAggregateParam(whereCondition, startDate, endDate, isBulkKra));
        aggregateParams.push(...this.getAggregateParam(quarter, Number(year)));
        aggregateParams.push(...this.getProjectionParam());
        aggregateParams.push(...this.getSortingParams(req));
        const aggregate = Project.aggregate(aggregateParams);
        return await Project.aggregatePaginate(aggregate, options);
    }
    /**
     * @desc This function is being used to get all projects
     * <AUTHOR>
     * @since 07/76/2022
     * @param {Object} req Request
     * @return {Object} response Success response
     */
    static async allProjects (req, user) {
        const aggregateParams = [];
        const query = { isActive: 1 };
        if (user.role !== CONSTANTS.ROLE.ADMIN &&
            user.role !== CONSTANTS.ROLE.BU &&
            !req.query.isUserReviewManager &&
            !req.query.isUserReportingManager &&
            !req.query.isGetLogsForReviewLogs
        ) {
            query.$or = [
                { pmUser:  mongoose.Types.ObjectId(user._id) },
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        if (req.query.isUserReviewManager) {
            query.$or = [
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        if (req.query.isUserReportingManager) {
            query.$or = [
                { pmUser: { $in: [mongoose.Types.ObjectId(user._id)] } }
            ];
        }
        if (user.role !== CONSTANTS.ROLE.ADMIN && req.query.isGetLogsForReviewLogs) {
            query.$or = [
                { pmUser: mongoose.Types.ObjectId(user._id) },
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        aggregateParams.push(
            {
                $match: query
            },
            {
                $unwind: {
                    path: '$users',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'users',
                    let: {
                        employeeId: {
                            $toObjectId: '$users.empId'
                        },
                        users: '$users'
                    },
                    pipeline: [
                        {
                            $match: {
                                $expr: {
                                    $eq: [
                                        '$_id',
                                        '$$employeeId'
                                    ]
                                }
                            }
                        },
                        {
                            $replaceRoot: {
                                newRoot: {
                                    $mergeObjects: [
                                        '$$users',
                                        '$$ROOT'
                                    ]
                                }
                            }
                        }
                    ],
                    as: 'users'
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'pmUser',
                    foreignField: '_id',
                    as: 'pmUser'
                }
            },
            {
                $unwind: {
                    path: '$pmUser',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'reviewManager',
                    foreignField: '_id',
                    as: 'reviewManager'
                }
            },
            {
                $unwind: {
                    path: '$reviewManager',
                    preserveNullAndEmptyArrays: true
                }
            },
            {
                $group: {
                    _id: '$_id',
                    projectName: {
                        $first: '$projectName'
                    },
                    pmUser: {
                        $first: '$pmUser'
                    },
                    reviewManager: {
                        $first: '$reviewManager'
                    },
                    startDate: {
                        $first: '$startDate'
                    },
                    endDate: {
                        $first: '$endDate'
                    },
                    team: {
                        $push: {
                            $first: '$users'
                        }
                    }
                }
            },
            {
                $project: {
                    _id: '$_id',
                    projectName: '$projectName',
                    'team.firstName': 1,
                    'team.lastName': 1,
                    'team.empRole': 1,
                    'team.empId': 1,
                    'team.startDate': 1,
                    'team.endDate': 1,
                    'reportingManager._id': '$pmUser._id',
                    'reportingManager.firstName': '$pmUser.firstName',
                    'reportingManager.lastName': '$pmUser.lastName',
                    'reviewManager._id': '$reviewManager._id',
                    'reviewManager.firstName': '$reviewManager.firstName',
                    'reviewManager.lastName': '$reviewManager.lastName',
                    startDate: '$startDate',
                    endDate: '$endDate'
                }
            }, {
                $sort: { projectName: 1 }
            }
        );
        return await Project.aggregate(aggregateParams);
    }

    /**
     * @desc This function is being used to get all projects for logs fetch
     * <AUTHOR>
     * @since 22/06/2021
     * @param {Object} req Request
     * @return {Object} response Success response
     */
    static async allLogProjects () {
        return await Logs.distinct('jiraProjectName').lean();
    }

    static getSortingParams (req) {
        const aggregateParams = [];
        if (req.query.sort && req.query.sortBy === 'projectName') {
            aggregateParams.push({
                $sort: { projectNameLower: parseInt(req.query.sort) }
            });
        }

        if (req.query.sort && req.query.sortBy === 'startDate') {
            aggregateParams.push({
                $sort: { startDate: parseInt(req.query.sort) }
            });
        }

        if (req.query.sort && req.query.sortBy === 'endDate') {
            aggregateParams.push({
                $sort: { endDate: parseInt(req.query.sort) }
            });
        }

        if (req.query.sort && req.query.sortBy === 'projectState') {
            aggregateParams.push({
                $sort: { projectState: parseInt(req.query.sort) }
            });
        }
        return aggregateParams;
    }

    /**
     * @desc This function is being used to get Aggregation Param.
     * <AUTHOR>
     * @since 27/03/2022
     * @param {Object} whereCondition
     * @param {Object} startDate
     * @param {Object} endDate
     */

    static getStaticAggregateParam (whereCondition, startDate, endDate, isBulkKra) {
        return [ {
            $match: whereCondition
        },
        {
            '$lookup': {
                'from': 'logs',
                let: { projectName: '$projectName' },
                'pipeline': [
                    {
                        '$match':  {
                            $expr: {
                                $and: [
                                    { $eq: ['$jiraProjectName', '$$projectName'] },
                                    {
                                        $gte: [{ $dateToString: { format: '%Y-%m-%d', date: '$logDate' } },
                                            startDate
                                        ]

                                    },
                                    {
                                        $lte: [{ $dateToString: { format: '%Y-%m-%d', date: '$logDate' } },
                                            endDate
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                ],
                'as': 'logDetails'
            }
        },
        {
            '$unwind': {
                'path': '$logDetails',
                'preserveNullAndEmptyArrays': !isBulkKra
            }
        },
        {
            '$group': {
                '_id': { userId: '$logDetails.userId', projectName: '$projectName' },
                'projectId': {
                    '$first': '$_id'
                },
                'userId': {
                    '$first': '$logDetails.userId'
                },
                'manDays': {
                    '$sum': '$logDetails.timeSpentHours'
                },
                'pmUser': {
                    '$first': '$pmUser'
                },
                'reviewManager': {
                    '$first': '$reviewManager'
                },
                'isActive': {
                    '$first': '$isActive'
                },
                'startDate': {
                    '$first': '$startDate'
                },
                'endDate': {
                    '$first': '$endDate'
                },
                'projectState': {
                    '$first': '$projectState'
                },
                'projectName': {
                    '$first': '$projectName'
                },
                'businessUnitId': {
                    '$first': '$businessUnitId'
                }
            }
        }];
    }

    static getAggregateParam (quarter, year) {
        return [ {
            '$lookup': {
                'from': 'users',
                'localField': 'userId',
                'foreignField': '_id',
                'as': 'userDetails'
            }
        }, {
            '$lookup': {
                'from': 'userkras',
                'let': {
                    'projectId': '$projectId',
                    'userId': '$userId'
                },
                'pipeline': [
                    {
                        '$match': {
                            '$expr': {
                                '$and': [
                                    {
                                        '$eq': [
                                            '$projectId', '$$projectId'
                                        ]
                                    }, {
                                        '$eq': [
                                            '$userId', '$$userId'
                                        ]
                                    }, {
                                        '$eq': [
                                            '$quarter', quarter
                                        ]
                                    }, {
                                        '$eq': [
                                            '$year', year
                                        ]
                                    }
                                ]
                            }
                        }
                    }
                ],
                'as': 'userkras'
            }
        }, {
            '$unwind': {
                'path': '$userDetails',
                'preserveNullAndEmptyArrays': true
            }
        }, {
            '$project': {
                '_id': '$_id',
                'projectId': 1,
                'logDetails': 1,
                'manDays': 1,
                'pmUser': 1,
                'isActive': '$isActive',
                'reviewManager': 1,
                'startDate': '$startDate',
                'endDate': '$endDate',
                'projectState': '$projectState',
                'teams': [
                    {
                        'empId': '$userId',
                        'manDays': { $divide: ['$manDays', 8] },
                        'checkUserKraExists': {
                            '$cond': {
                                'if': {
                                    '$gt': [
                                        {
                                            '$size': '$userkras'
                                        }, 0
                                    ]
                                },
                                'then': 1,
                                'else': 0
                            }
                        },
                        'firstName': '$userDetails.firstName',
                        'lastName': '$userDetails.lastName',
                        'empRole':  '$userDetails.designation'
                    }
                ],
                'projectName': 1,
                businessUnitId: 1
            }
        }, {
            '$lookup': {
                'from': 'users',
                'localField': 'pmUser',
                'foreignField': '_id',
                'as': 'pmUser'
            }
        }, {
            '$unwind': {
                'path': '$pmUser',
                'preserveNullAndEmptyArrays': true
            }
        }, {
            '$lookup': {
                'from': 'users',
                'localField': 'reviewManager',
                'foreignField': '_id',
                'as': 'reviewManager'
            }
        }, {
            '$unwind': {
                'path': '$reviewManager',
                'preserveNullAndEmptyArrays': true
            }
        }, {
            '$sort': {
                'manDays': -1
            }
        }
        ];
    }

    static getProjectionParam () {
        return [{
            '$group': {
                '_id': '$projectId',
                'pmUser': {
                    '$first': '$pmUser'
                },
                'reviewManager': {
                    '$first': '$reviewManager'
                },
                'isActive': {
                    '$first': '$isActive'
                },
                'startDate': {
                    '$first': '$startDate'
                },
                'endDate': {
                    '$first': '$endDate'
                },
                'projectState': {
                    '$first': '$projectState'
                },
                'projectName': {
                    '$first': '$projectName'
                },
                'businessUnitId': {
                    '$first': '$businessUnitId'
                },
                'team': {
                    '$push': {
                        '$first': '$teams'
                    }
                }
            }
        }, {
            '$project': {
                '_id': '$_id',
                'projectName': '$projectName',
                'projectNameLower': {
                    '$toLower': '$projectName'
                },
                'isActive': '$isActive',
                team: { $filter: {
                    input: '$team',
                    as: 'item',
                    cond: { $ne: ['$$item.empId', null] }
                } },
                'reportingManager._id': '$pmUser._id',
                'reportingManager.firstName': '$pmUser.firstName',
                'reportingManager.lastName': '$pmUser.lastName',
                'reviewManager._id': '$reviewManager._id',
                'reviewManager.firstName': '$reviewManager.firstName',
                'reviewManager.lastName': '$reviewManager.lastName',
                'startDate': '$startDate',
                'endDate': '$endDate',
                'projectState': '$projectState',
                businessUnitId: 1
            }
        }, {
            '$project': {
                '_id': 1,
                'projectName': 1,
                'projectNameLower': 1,
                'isActive': 1,
                'team': 1,
                'reportingManager._id': 1,
                'reportingManager.firstName': 1,
                'reportingManager.lastName': 1,
                'reviewManager._id': 1,
                'reviewManager.firstName': 1,
                'reviewManager.lastName': 1,
                'startDate': 1,
                'endDate': 1,
                'projectState': 1,
                'businessUnitId': 1,
                'hasTeam': {
                    '$cond': {
                        'if': {
                            '$gt': [
                                {
                                    '$size': '$team'
                                }, 0
                            ]
                        },
                        'then': 1,
                        'else': 0
                    }
                },
                'toUploadKra': {
                    '$in': [
                        0, '$team.checkUserKraExists'
                    ]
                }
            }
        }, {
            '$sort': {
                'toUploadKra': -1,
                'hasTeam': -1,
                'projectNameLower': 1
            }
        }];
    }

    /**
     * @desc This function is being used to get all first and last date of quarter.
     * <AUTHOR>
     * @since 06/03/2022
     * @param {Object} req Request
     * @return {Object} response Success response
     */
    static getQuarterDates (quarter, year) {
        const quarterMonth = {
            Q1: 4,
            Q2: 7,
            Q3: 10,
            Q4: 1
        };
        const startingMonth = quarterMonth[quarter];
        const startDate = new Date(year, startingMonth - 1, 1);
        const lastMonth = startingMonth + 2;
        const endDate = new Date(year, lastMonth, 0);
        return {
            firstDate: MOMENT(startDate).format('YYYY-MM-DD'),
            lastDate: MOMENT(endDate).format('YYYY-MM-DD')
        };
    }

    static getCurrentQuarterYear () {
        const today = MOMENT();
        const year = today.year();
        const month = today.format('MMMM');
        const monthToQuarter = {
            January: 'Q4',
            February: 'Q4',
            March: 'Q4',
            April: 'Q1',
            May: 'Q1',
            June: 'Q1',
            July: 'Q2',
            August: 'Q2',
            September: 'Q2',
            October: 'Q3',
            November: 'Q3',
            December: 'Q3'
        };
        return { year, quarter: monthToQuarter[month] };
    }

    /**
     * @desc This function is being used to get Query Param for search.
     * <AUTHOR>
     * @since 27/03/2022
     * @param {Object} req
     * @param {Object} user
     * @return {Object} whereCondition
     */
    static async getQueryParam (req, user) {
        const whereCondition = {};
        if (user.role !== CONSTANTS.ROLE.ADMIN && user.role !== CONSTANTS.ROLE.BU) {
            whereCondition.$or = [{ users: { $elemMatch: { empId: mongoose.Types.ObjectId(user._id) } } },
                { pmUser: { $in: [mongoose.Types.ObjectId(user._id)] } },
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        if (req.query.name) {
            whereCondition.projectName = {
                $regex: `.*${req.query.name}.*`,
                $options: 'i'
            };
        }
        return whereCondition;
    }

    /**
    * @desc This function is being used to get all projects by log count
    * <AUTHOR>
    * @since 07/76/2022
    * @param {Object} req Request
    * @return {Object} response Success response
    */

    static async allProjectsByLogCount (req, user) {
        const aggregateParams = [];
        const query = { isActive: 1 };

        if (user.role !== CONSTANTS.ROLE.ADMIN &&
            user.role !== CONSTANTS.ROLE.BU &&
            !req.query.isUserReviewManager &&
            !req.query.isUserReportingManager &&
            !req.query.isGetLogsForReviewLogs
        ) {
            query.$or = [
                { pmUser: mongoose.Types.ObjectId(user._id) },
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        if (req.query.isUserReviewManager) {
            query.$or = [
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        if (req.query.isUserReportingManager) {
            query.$or = [
                { pmUser: { $in: [mongoose.Types.ObjectId(user._id)] } }
            ];
        }
        if (user.role !== CONSTANTS.ROLE.ADMIN && req.query.isGetLogsForReviewLogs) {
            query.$or = [
                { pmUser: mongoose.Types.ObjectId(user._id) },
                { reviewManager: mongoose.Types.ObjectId(user._id) }
            ];
        }
        const startDate = req.query.startDate ? MOMENT(req.query.startDate).format('YYYY-MM-DD') : getQuarterDates.firstDate;
        const endDate = req.query.endDate ? MOMENT(req.query.endDate).format('YYYY-MM-DD') : getQuarterDates.lastDate;
        aggregateParams.push(
            { $match: query },

            {
                $lookup: {
                    from: 'users',
                    let: { employeeId: { $toObjectId: { $arrayElemAt: ['$users.empId', 0] } } },
                    pipeline: [
                        { $match: { $expr: { $eq: ['$_id', '$$employeeId'] } } },
                        { $project: { _id: 1, firstName: 1, lastName: 1 } }
                    ],
                    as: 'users'
                }
            },
            { $unwind: { path: '$users', preserveNullAndEmptyArrays: true } },

            {
                $lookup: {
                    from: 'users',
                    localField: 'pmUser',
                    foreignField: '_id',
                    as: 'pmUser'
                }
            },
            { $unwind: { path: '$pmUser', preserveNullAndEmptyArrays: true } },

            {
                $lookup: {
                    from: 'users',
                    localField: 'reviewManager',
                    foreignField: '_id',
                    as: 'reviewManager'
                }
            },
            { $unwind: { path: '$reviewManager', preserveNullAndEmptyArrays: true } },

            {
                $project: {
                    _id: 1,
                    projectName: 1,
                    'reportingManager._id': '$pmUser._id',
                    'reportingManager.firstName': '$pmUser.firstName',
                    'reportingManager.lastName': '$pmUser.lastName',
                    'reviewManager._id': '$reviewManager._id',
                    'reviewManager.firstName': '$reviewManager.firstName',
                    'reviewManager.lastName': '$reviewManager.lastName',
                    startDate: startDate,
                    endDate: endDate
                }
            },
            { $sort: { projectName: 1 } }
        );


        return await Project.aggregate(aggregateParams);
    }
}
module.exports = ListProjectService;
