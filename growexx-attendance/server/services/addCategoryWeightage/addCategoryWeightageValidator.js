const GeneralError = require('../../util/GeneralError');
const validation = require('../../util/validation');

/**
 * Class represents validations for category weightage.
 */
class AddCategoryWeightageValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
        this.locale = locale;
    }

    /**
     * @desc This function is being used to validate add a category weightage
     * <AUTHOR>
     * @since 24/08/2022
     */
    async validationAddCategoryWeightage () {
        const { categoryWeightages } = this.body;
        let sum = 0;
        const isSameQuarterYearDesignation = { quarter: categoryWeightages[0].quarter, year: categoryWeightages[0].year,
            designation: categoryWeightages[0].designation };
        for ( const categoryWeightage of categoryWeightages) {
            if (isSameQuarterYearDesignation.quarter !== categoryWeightage.quarter) {
                throw new GeneralError(this.locale('INVALID_QUARTER_ALL'));
            }
            if (isSameQuarterYearDesignation.year !== categoryWeightage.year) {
                throw new GeneralError(this.locale('INVALID_YEAR_ALL'));
            }
            if (isSameQuarterYearDesignation.designation !== categoryWeightage.designation) {
                throw new GeneralError(this.locale('INVALID_DESIGNATION_ALL'));
            }
            super.quarter(categoryWeightage.quarter, 'quarter');
            super.year(categoryWeightage.year, 'year');
            super.category(categoryWeightage.category, 'category');
            super.weightage(categoryWeightage.weightage, 'weightage');
            await super.designation(categoryWeightage.designation, 'designation');
            sum += categoryWeightage.weightage;
        }
        if (sum !== 100) {
            throw new GeneralError(this.locale('CATEGORY_WEIGHTAGE_MORE_THAN_HUNDRED'));
        }
    }
}
module.exports = AddCategoryWeightageValidator;
