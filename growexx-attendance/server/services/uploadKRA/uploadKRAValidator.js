const validation = require("../../util/validation");
const GeneralError = require("../../util/GeneralError");
const constants = require("../../util/constants");

/**
 * Class represents validations for import kra stock data
 */
class UploadKRAValidator extends validation {
  constructor(body, locale) {
    super(locale);
    this.body = body;
  }

  /**
   * @desc This function is being used to validate validate upload/import kra file
   * <AUTHOR>
   * @param {file} validateUserDocumentUpload validateUserDocumentUpload
   * @since 07/07/2022
   */

  async validateUploadKRAFile(file) {
    if (!file) {
      throw new GeneralError(this.__(this.REQUIRED, "File"), 400);
    }
    await this.uploadKRAFileType(file.mimetype);
  }

  /**
   * @desc This function is being used to validate upload kra request
   * <AUTHOR>
   * @since 27/09/2022
   */

  async validateUploadKRA() {
    const { quarter, year, designation } = this.body.body;
    const file = this.body.file;
    await this.validateUploadKRAFile(file);
    super.quarter(quarter);
    super.year(_.toInteger(year));
    await super.designation(designation);
  }

  /**
   * @desc This function is being used to validate upload KRA File Type
   * <AUTHOR>
   * @param {file} uploadKRAFileType uploadKRAFileType
   * @since 07/07/2022
   */
  async uploadKRAFileType(mimeType) {
    if (!mimeType || !constants.LEAVE_FILE_TYPE.includes(mimeType)) {
      throw new GeneralError(
        this.__(this.FILE_TYPE_NOT_VALID, "CSV, XLS and XLSX"),
        400
      );
    }
  }
}

module.exports = UploadKRAValidator;
