const { default: mongoose } = require('mongoose');
const Rag = require('../../models/rag.model');
const SprintMetrics = require('../../models/sprintMetrics.model');
const sprintMetricsModel = require('../../models/sprintMetrics.model');
const PtCronStatus = require('../../models/ptCronStatus.model');
const getRagStatus = require('../../../crons/ragStatusHelper');
const constants = require('../../util/constants');
const RagValidator = require('./ragValidator');
const moment = require('moment');

class RagService {
    /**
   * @desc This function fetches rag reports from Db.
   * @param {Object} req - Express request object.
   * @param {String} req.query.project - Project name.
   * @param {String} req.query.user - User id.
   * @param {String} req.query.start - Start date of range to find when sprint ends.
   * @param {String} req.query.end - End date of range to find when sprint ends.
   * @returns {Array} - Array of rag reports.
   */
    static async getRagReport (req) {
        const { project, user, start, end } = req.query;
        const aggregateParams = [];
        const matchParams = {};
        const matchUserParams = {};

        if (project) {
            matchParams.project = project;
        }

        if (start && end) {
            const startDate = moment(start).startOf('day').toDate();
            const endDate = moment(end).endOf('day').toDate();
            matchParams.$or = [
                {
                    sprintStart: { $lte: endDate },
                    sprintEnd: { $gte: startDate }
                }
            ];
        }
        if (user) {
            matchUserParams.userId = mongoose.Types.ObjectId(user);
        }

        if (Object.keys(matchParams).length > 0) {
            aggregateParams.push({
                $match: matchParams
            });
        }
        // First sort the collection by project and then by sprint end date.
        // Group the collection by project and then by sprint end date.
        // Add fields to the collection.
        aggregateParams.push(
            ...[
                {
                    $sort: { project: 1, sprintEnd: -1 }
                },
                {
                    $group: {
                        _id: '$project',
                        totalSprints: { $push: '$$ROOT' },
                        latestSprint: { $first: '$$ROOT' }
                    }
                },
                {
                    $addFields: {
                        remainingSprints: {
                            $slice: ['$totalSprints', 1, { $size: '$totalSprints' }]
                        }
                    }
                },
                {
                    $sort: { _id: 1 }
                },
                {
                    $facet: {
                        latestSprint: [
                            { $unwind: { path: '$latestSprint' } },
                            {
                                $replaceRoot: {
                                    newRoot: '$latestSprint'
                                }
                            }
                        ],
                        remainingSprints: [
                            { $unwind: { path: '$remainingSprints' } },
                            {
                                $replaceRoot: {
                                    newRoot: '$remainingSprints'
                                }
                            }
                        ]
                    }
                },
                {
                    $project: {
                        _id: 1,
                        sprints: {
                            $concatArrays: ['$latestSprint', '$remainingSprints']
                        }
                    }
                },
                {
                    $unwind: '$sprints'
                },
                {
                    $replaceRoot: {
                        newRoot: '$sprints'
                    }
                },
                {
                    $addFields: {
                        teamLength: {
                            $size: '$team'
                        }
                    }
                },
                {
                    $unwind: '$team'
                },
                {
                    $lookup: {
                        from: 'users',
                        localField: 'team.member',
                        foreignField: 'label',
                        as: 'users'
                    }
                },
                {
                    $lookup: {
                        from: 'projects',
                        localField: 'project',
                        foreignField: 'projectName',
                        as: 'projectInfo'
                    }
                },
                {
                    $lookup: {
                        from: 'portals',
                        localField: 'project',
                        foreignField: 'name',
                        as: 'portalInfo'
                    }
                },
                {
                    $project: {
                        project: 1,
                        sprintReport: 1,
                        sprintNumber: 1,
                        teamLength: 1,
                        member: '$team.member',
                        b2dCount: '$team.b2dCount',
                        solvedBugCount: '$team.solvedBugCount',
                        sprintMetrics: '$team.sprintMetrics',
                        effortVarianceIndivisual: '$team.effortVariance',
                        memberClientEscalation: '$team.escalationCount',
                        memberId: '$team._id',
                        plannedEfforts: '$team.plannedEfforts',
                        spentHours: '$team.spentHours',
                        openCloseRatio: 1,
                        effortVariance: 1,
                        bugsReported: {
                            $concat: [
                                { $toString: '$resolvedBugs' },
                                '/',
                                { $toString: '$totalBugs' }
                            ]
                        },
                        totalBugs: 1,
                        bugsCreatedDuringSprint: 1,
                        spillOverBugs: 1,
                        unlabelledBugsCount: 1,
                        unlabelledBugsCountJQL: 1,
                        techAudit: 1,
                        processAudit: 1,
                        resolvedBugs: 1,
                        clientEscalations: 1,
                        risksIdentified: 1,
                        mitigationPlan: 1,
                        comments: 1,
                        createdAt: 1,
                        updatedAt: 1,
                        sprintEnd: 1,
                        sprintStart: 1,
                        boardId: 1,
                        boardKey: 1,
                        jiraSprintId: 1,
                        userId: {
                            $arrayElemAt: ['$users._id', 0]
                        },
                        projectInfo: {
                            _id: { $arrayElemAt: ['$projectInfo._id', 0] },
                            pmUser: { $arrayElemAt: ['$projectInfo.pmUser', 0] }
                        },
                        portalInfo: 1,
                        freeze: 1,
                        completedStoriesCount: 1,
                        totalPlannedStories: 1,
                        totalPlannedEfforts: 1,
                        totalSpentEfforts: 1,
                        deliveryHeadComments: 1,
                        apiCreated: 1,
                        users: 1,
                        team: 1,
                        doneIssues: 1
                    }
                },
                {
                    $match: {
                        ...matchUserParams
                    }
                }
            ]
        );
        console.log("aggregateParams : ", JSON.stringify(aggregateParams));
        const data = await Rag.aggregate(aggregateParams);
        return this.modifyRagPayload(data);
    }

    /**
     * Modifies the RAG payload by adding additional fields and transforming existing ones.
     * @param {Array} payload - The RAG payload to be modified
     * @return {Array} The modified RAG payload
     */
    static modifyRagPayload (payload) {
        const res = [];
        let idCache = '';
        let indexCache = 0;
        let projectIndexCache = 0;
        const DEVELOPER_DESIGNATIONS = ['Jr. Software Engineer', 'Software Engineer', 'Sr. Software Engineer'];

        // First pass: Group data by sprint ID to collect all team members for each sprint
        const sprintGroups = {};
        for (let i = 0; i < payload.length; i++) {
            const sprintId = payload[i]._id.toString();
            if (!sprintGroups[sprintId]) {
                sprintGroups[sprintId] = {
                    sprintData: payload[i],
                    teamMembers: []
                };
            }
            // Collect team member escalation data - fix: use memberClientEscalation from root level
            sprintGroups[sprintId].teamMembers.push({
                member: payload[i].member,
                escalationCount: payload[i].memberClientEscalation || 0
            });
        }

        // Calculate overall RAG for each sprint considering all team members
        const sprintRagColors = {};
        Object.keys(sprintGroups).forEach(sprintId => {
            const sprintData = sprintGroups[sprintId].sprintData;
            const teamMembers = sprintGroups[sprintId].teamMembers;

            // Create a payload with all team member escalation data for accurate RAG calculation
            const ragPayload = {
                ...sprintData,
                team: teamMembers
            };

            sprintRagColors[sprintId] = this.getOverallRag(ragPayload);
        });

        // Second pass: Build the result array with correct overall RAG colors
        for (let i = 0; i < payload.length; i++) {
            const sprintId = payload[i]._id.toString();
            const overallRag = sprintRagColors[sprintId];

            if (payload[i]._id.toString() !== idCache) {
                projectIndexCache = i;

                // JQL to get all not done stories.
                // eslint-disable-next-line max-len
                const url =
                    payload[i]?.portalInfo?.length > 0
                        ? payload[i]?.portalInfo[0]?.url
                        : 'growexx.atlassian.net';
                // eslint-disable-next-line max-len
                const bugString = `https://${url}/jira/software/c/projects/${payload[i].boardKey}/issues/?jql=project%20%3D%20%22${payload[i].boardKey}%22%20and%20type%20%3D%20Bug%20and%20sprint%20%3D%20${payload[i].jiraSprintId}%20AND%20status%20NOT%20IN%20%28Done%29%20ORDER%20BY%20created%20DESC`;
                indexCache += 1;

                res.push({
                    ...payload[i],
                    index: indexCache,
                    overallRag,
                    bugLink: bugString,
                    effortVarianceIndivisual: payload[i].effortVarianceIndivisual || null,
                    spentHours: payload[i].spentHours || null,
                    plannedEfforts: payload[i].plannedEfforts || null,
                    developersCount: DEVELOPER_DESIGNATIONS.includes(_.get(payload[i], 'users[0].designation')) ? 1 : 0
                });
                idCache = payload[i]._id.toString();
            } else {
                res.push({
                    ragId: payload[i]._id,
                    freeze: payload[i].freeze,
                    projectInfo: payload[i].projectInfo,
                    memberId: payload[i].memberId,
                    member: payload[i].member,
                    b2dCount: payload[i].b2dCount,
                    plannedEfforts: payload[i].plannedEfforts,
                    spentHours: payload[i].spentHours,
                    solvedBugCount: payload[i].solvedBugCount,
                    sprintMetrics: payload[i].sprintMetrics,
                    effortVarianceIndivisual: payload[i].effortVarianceIndivisual,
                    index: indexCache,
                    memberClientEscalation: payload[i].memberClientEscalation || 0,
                    sprintEnd: payload[i].sprintEnd,
                    overallRag // Add overallRag to member rows as well
                });

                if (DEVELOPER_DESIGNATIONS.includes(_.get(payload[i], 'users[0].designation'))) {
                    res[projectIndexCache].developersCount += 1;
                }
            }
        }
        return res;
    }
    /**
   * @desc This function modifies rag report payload as per integration requirement.
   * @param {Object[]} payload The rag report payload.
   * @returns {Object[]} The modified rag report payload.
   * @param {String} req.query.user - User id.
   * @param {String} req.query.start - Start date of range to find when sprint ends.
   * @param {String} req.query.end - End date of range to find when sprint ends.
   * @returns {Array} - Array of sprint metrics.
   */
    static async getSprintMetrics (req) {
        const { project, user, start, end } = req.query;
        const matchParams = {};
        const matchUserParams = {};
        // matchParams.repo = 'Overall';

        if (project) {
            matchParams.projectName = project;
        }

        if (user) {
            matchUserParams.userId = mongoose.Types.ObjectId(user);
        }

        if (start && end) {
            matchParams.endDate = {
                $gte: new Date(start),
                $lte: new Date(end)
            };
        }

        const aggregateParams = [
            { $match: matchParams },
            {
                $sort: { projectName: 1, endDate: -1 }
            },
            {
                $group: {
                    _id: '$projectName',
                    totalSprints: { $push: '$$ROOT' },
                    latestSprint: { $first: '$$ROOT' }
                }
            },
            {
                $addFields: {
                    remainingSprints: {
                        $slice: ['$totalSprints', 1, { $size: '$totalSprints' }]
                    }
                }
            },
            {
                $sort: { _id: 1 }
            },
            {
                $facet: {
                    latestSprint: [
                        { $unwind: { path: '$latestSprint' } },
                        {
                            $replaceRoot: {
                                newRoot: '$latestSprint'
                            }
                        }
                    ],
                    remainingSprints: [
                        { $unwind: { path: '$remainingSprints' } },
                        {
                            $replaceRoot: {
                                newRoot: '$remainingSprints'
                            }
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    sprints: { $concatArrays: ['$latestSprint', '$remainingSprints'] }
                }
            },
            {
                $unwind: '$sprints'
            },
            {
                $replaceRoot: {
                    newRoot: '$sprints'
                }
            },
            {
                $lookup: {
                    from: 'users',
                    localField: 'resource',
                    foreignField: 'githubUsernames',
                    as: 'users'
                }
            },
            {
                $addFields: {
                    userId: {
                        $arrayElemAt: ['$users._id', 0]
                    }
                }
            },
            {
                $match: {
                    ...matchUserParams
                }
            }
        ];

        const data = await sprintMetricsModel.aggregate(aggregateParams);
        return this.processSprintMetricsPayload(data);
    }

    /**
     * @desc This function modifies sprint metrics payload as per integration requirement.
     * It takes an array of objects and groups it by project and sprint number.
     * It then creates a new array of objects with the required properties.
     * @param {Object[]} payload The sprint metrics payload.
     * @returns {Object[]} The modified sprint metrics payload.
     */

    // static processSprintMetricsPayload(payload) {
    //     const res = [];
    //     const projectCache = {};
    //     for (let i = 0; i < payload.length; i++) {
    //         const projectCacheKey = `${payload[i].projectName}-${payload[i].sprintNumber}`;
    //         if (projectCache[projectCacheKey]) {
    //             const temp = { ...payload[i] };
    //             delete temp.projectName;
    //             delete temp.sprintNumber;
    //             projectCache[projectCacheKey].push(temp);
    //         } else {
    //             projectCache[projectCacheKey] = [{ ...payload[i] }];
    //         }
    //     }

    //     Object.keys(projectCache).forEach((project, projectIndex) => {
    //         projectCache[project].forEach((d, index) => {
    //             if (index === 0) {
    //                 res.push({
    //                     ...d,
    //                     projectSpan: projectCache[project].length,
    //                     index: projectIndex
    //                 });
    //             } else {
    //                 res.push({ ...d, index: projectIndex });
    //             }
    //         });
    //     });

    //     return res;
    // }

    static processSprintMetricsPayload (data) {
        // Step 1: Group data by projectName and sprintNumber
        const groupedData = {};
        data.forEach((item) => {
            const { projectName, sprintNumber, repo } = item;
            const key = `${projectName}-${sprintNumber}`;

            if (!groupedData[key]) {
                groupedData[key] = {};
            }

            if (!groupedData[key][repo]) {
                groupedData[key][repo] = [];
            }

            groupedData[key][repo].push(item);
        });
        // Step 2: Flatten the grouped structure while removing duplicates
        const result = [];
        Object.keys(groupedData).forEach((groupKey, groupIndex) => {
            const [projectName, sprintNumber] = groupKey.split('-');

            // Sort repos to ensure 'Overall' appears first
            const sortedRepos = Object.keys(groupedData[groupKey]).sort((a, b) => (a === "Overall" ? -1 : b === "Overall" ? 1 : 0));

            sortedRepos.forEach((repo, repoIndex) => {
                groupedData[groupKey][repo].forEach((item, itemIndex) => {
                    const newItem = { ...item };

                    // Remove projectName & sprintNumber for duplicate rows (except first occurrence)
                    if (repoIndex > 0 || itemIndex > 0) {
                        delete newItem.projectName;
                        delete newItem.sprintNumber;
                    }

                    // Remove repo for duplicate rows (except first occurrence)
                    if (itemIndex > 0) {
                        delete newItem.repo;
                    }

                    result.push({ ...newItem, index: groupIndex });
                });
            });
        });

        return result;
    }

    /**
     * Returns the flag corresponding to the given value based on the provided ranges.
     * @param {number} value - The value to be checked against the ranges.
     * @param {Array<Object>} ranges - An array of objects containing min, max, and flag properties.
     * @return {number} The flag corresponding to the value, or 0 if no match is found.
     */
    static getFlag (value, ranges) {
        for (let i = 0; i < ranges.length; i++) {
            if (value >= ranges[i].min && value < ranges[i].max) {
                return ranges[i].flag;
            }
        }
        return 0;
    }

    /**
     * Calculates the overall RAG color for the given payload.
     * @param {Object} payload - The payload containing openCloseRatio, effortVariance, totalBugs, and resolvedBugs.
     * @return {string} The overall RAG color.
     */
    static getOverallRag (payload) {
        const { openCloseRatio, effortVariance, totalBugs, resolvedBugs } = payload;
        const openCloseFlag = this.getFlag(openCloseRatio, [
            { min: 75, max: 101, flag: 2 },
            { min: 55, max: 75, flag: 1 }
        ]);

        const effortVarianceFlag = this.getFlag(effortVariance, [
            { min: Number.NEGATIVE_INFINITY, max: 10, flag: 2 },
            { min: 10, max: 20, flag: 1 }
        ]);

        let bugFlag = this.getFlag(resolvedBugs, [
            { min: 0, max: 4, flag: 2 },
            { min: 4, max: 6, flag: 1 },
            { min: 6, max: Number.POSITIVE_INFINITY, flag: 0 }
        ]);
        bugFlag = totalBugs - resolvedBugs ? 0 : bugFlag;
        const flags = [openCloseFlag, effortVarianceFlag, bugFlag];

        let color;
        if (flags.includes(0)) {
            color = constants.COLOR_CODE.RED;
        } else if (flags.includes(1)) {
            color = constants.COLOR_CODE.AMBER;
        } else {
            color = constants.COLOR_CODE.GREEN;
        }
        if (payload.clientEscalations && Number(payload.clientEscalations) > 0) {
            color = constants.COLOR_CODE.RED;
        }
        if (payload.memberClientEscalation && Number(payload.memberClientEscalation) > 0) {
            color = constants.COLOR_CODE.RED;
        }
        if (payload.team && Array.isArray(payload.team)) {
            const isAllClear = payload.team.every((item) => item.escalationCount === 0);
            if (!isAllClear) {
                color = constants.COLOR_CODE.RED;
            }
        }

        return color;
    }

    /**
     * @desc Service to download the rag report
     * @param {Object} req Express request object
     * @return {Object} An object containing the rag report and sprint metrics report
     */
    static async downloadRagReport (req) {
        const ragReport = await this.getRagReport(req);
        const sprintMetricsReport = await this.getSprintMetrics(req);
        return { ragReport, sprintMetricsReport };
    }

    /**
     * @desc Service to change rag details
     * @param {Object} req Request
     * @param {Object} res Response
     * @param {String} locale The locale for the request
     * @return {Promise<void>}
     */

    static async changeRagDetails (req, locale) {
        const ragValidator = new RagValidator(req.query, locale);
        ragValidator.validationChangeRagDetailsPayload(req.body);
        const { id, field, value, memberId } = req.body;
        if (field === CONSTANTS.RAG_EDITABLE_COLUMNS.MEMBER_CLIENT_ESCALATION && memberId) {
            await this.updateRagMemberEscalation(req.body);
        } else {
            await Rag.findByIdAndUpdate(id, { [field]: value });
        }
    }

    /**
     * @desc Service to update the member escalation count in the rag document.
     * @param {Object} payload The payload containing the rag id, escalation count and member.
     * @return {Promise<void>}
     */
    static async updateRagMemberEscalation (payload) {
        const { id, value: escalationCount, member } = payload;

        await Rag.findOneAndUpdate(
            { _id: id, 'team.member': member },
            { $set: { 'team.$.escalationCount': escalationCount } },
            { upsert: true }
        );
    }

    /**
   * @desc Service to change sprint metrics details
   * @param {Object} req Request
   * @param {Object} res Response
   */
    static async changeSprintMetricsDetails (req, locale) {
        const ragValidator = new RagValidator(req.query, locale);
        ragValidator.validationChangeRagDetailsPayload(req.body);
        const { id, field, value } = req.body;
        await SprintMetrics.findByIdAndUpdate(id, { [field]: value });

    }
    /**
     * @desc Service to freeze the given rag details. If ragIds is not provided, all rag and sprint metrics will be frozen.
     * @param {Object} req Request
     * @param {string[]} req.body.ragIds Rag Ids
     * @return {Promise<string>} 'Report Freezed successfully'
     */
    static async freezeRagDetails (req) {
        const { ragIds = [] } = req.body;
        if (!ragIds.length) {
            await Rag.updateMany({}, { freeze: true });
            await SprintMetrics.updateMany({}, { freeze: true });
        } else {
            for (const id of ragIds) {
                const rag = await Rag.findByIdAndUpdate(id, { freeze: true });
                const query = {
                    projectName: rag.project,
                    sprintNumber: rag.sprintNumber
                };
                await SprintMetrics.updateMany(
                    query,
                    {
                        $set: {
                            freeze: true,
                            projectName: rag.project,
                            sprintNumber: rag.sprintNumber
                        }
                    },
                    {
                        upsert: true
                    }
                );
            }
        }
        return 'Report Freezed successfully';
    }

    /**
     * @desc Service to trigger the cron to generate Rag and Sprint Metrics Reports for the given project Id.
     * @param {Object} req Request
     * @param {string} req.query.projectId Project Id
     * @param {string} locale Locale to use for the response
     * @return {Promise<void>}
     */
    static async triggerCron (req, locale) {
        const projectId = req.query.projectId;
        const Validator = new RagValidator(req.query, locale);
        Validator.validateProjectId(projectId);
        const currentCron = await PtCronStatus.findOne({});
        if (!currentCron.isRagCronRunning) {
            await getRagStatus('all', true, projectId);
        }
    }
}


module.exports = RagService;
