const User = require('../../models/user.model');
const AddUserValidator = require('./addUserValidator');
const GeneralError = require('../../util/GeneralError');
const Crypt = require('../../util/crypt');

/**
 * Class represents services for add a new user.
 */
class AddUserService {

    /**
     * @desc This function is being used to add a new user
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addUser (req, user, locale) {
        const Validator = new AddUserValidator(req.body, locale);
        await Validator.validationAddUser();

        const userEmail = req.body.email.toLowerCase();
        const dojDate = MOMENT(req.body.dateOfJoining, 'YYYY-MM-DD');

        const checkUser = await User.findOne({
            $or: [
                { label: { $in: req.body.label } },
                { email: userEmail },
                { employeeId: req.body.employeeId }
            ]
        }, { _id: 1, label: 1, email: 1, employeeId: 1 });

        if (checkUser && checkUser.id) {
            if (_.intersection(checkUser.label, req.body.label).length) {
                throw new GeneralError(locale('LABEL_EXISTS'), 400);
            }

            if (checkUser.email === userEmail) {
                throw new GeneralError(locale('EMAIL_EXISTS'), 400);
            }

            if (checkUser.employeeId === req.body.employeeId) {
                throw new GeneralError(locale('EMPLOYEE_ID_EXISTS'), 400);
            }
        }
        const { employeeId, password, firstName, lastName, label, designation, level, businessUnit } = req.body;
        return await User.create({
            employeeId,
            firstName,
            lastName,
            label,
            designation,
            level,
            businessUnit,
            email: userEmail,
            doj: dojDate,
            password: await Crypt.enCryptPassword(password),
            role: CONSTANTS.ROLE.USER
        });
    }

}

module.exports = AddUserService;
