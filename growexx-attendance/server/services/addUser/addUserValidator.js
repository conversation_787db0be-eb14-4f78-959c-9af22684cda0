const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for Jira portal add.
 */
class AddUserValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate add a user
     * <AUTHOR>
     * @since 06/04/2021
     */
    async validationAddUser () {
        const { employeeId, email, password, firstName, lastName, label, dateOfJoining, designation, level, businessUnit } = this.body;
        this.employeeId(employeeId, 'Employee id' );
        super.email(email, 'Email' );
        this.password(password, 'Password' );
        super.firstName(firstName, 'First name');
        super.lastName(lastName, 'Last name');
        super.label(label, 'Label');
        super.dateOfJoining(dateOfJoining, 'Date of joining');
        await super.designation(designation, 'Designation');
        super.businessUnit(businessUnit, 'Business Unit');
        super.userLevel(level);
    }

    /**
     * @desc This function is being used to validate employee id of a user
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Number} employeeId employeeId
     */
    employeeId (employeeId, field) {
        if (!employeeId) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }

        if (isNaN(employeeId)) {
            throw new GeneralError(this.__(this.NOT_VALID, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate password
     * <AUTHOR>
     * @since 06/04/2021
     * @param {string} password password
     */
    password (password, field) {
        if (!password) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }

        // For testing purposes, we'll accept any non-empty password
        if (typeof password !== 'string' || password.length < 1) {
            throw new GeneralError(this.__(this.INVALID, field), 400);
        }
    }
}

module.exports = AddUserValidator;
