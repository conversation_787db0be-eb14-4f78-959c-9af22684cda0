const UploadUserLeaveValidator = require('./uploadLeaveValidator');
const Leave = require('../../models/leave.model');
const User = require('../../models/user.model');
const xlsx = require('node-xlsx').default;
const moment = require('moment-timezone');
const { LEAVE_TYPE, HALF_DAY_STRING, WORK_FROM_HOME_STRING } = require('../../util/constants');
const Utils = require('../../util/utilFunctions');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents services for upload user leave.
 */
class UploadUserLeaveService {

    /**
     * @desc This function is being used to upload user leave
     * <AUTHOR>
     * @since 13/05/2021
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async uploadUserLeave(req, locale) {
        const Validator = new UploadUserLeaveValidator(null, locale);
        await Validator.validateUploadLeavesFile(req.file);
        const parsedData = UploadUserLeaveService.getFileParsedData(req.file.buffer, locale);
        const employeeMap = await User.findEmployeeMap(parsedData.employeeIds);
        const validLeaves = [];
        for (const leave of parsedData.leaveInputArray) {
            if (employeeMap[leave.employeeId]) {
                const leaveDate = moment(leave.leaveDate).format("YYYY-MM-DD");
                const leaveObj = new Leave({
                    ...leave,
                    /** @note in order to avoid "userId, leaveDate, leaveType" uniqueness */
                    startDate: `${leaveDate}T00:00:00.000+00:00`,
                    endDate: `${leaveDate}T00:00:00.000+00:00`,
                    leaveDate: `${leaveDate}T00:00:00.000+00:00`,
                    userId: employeeMap[leave.employeeId],
                    timeSpentHours: leave.timeSpentHours,
                    duration: leave.duration
                });

                try {
                    await leaveObj.validate();
                    validLeaves.push(leaveObj);
                } catch (error) {
                    CONSOLE_LOGGER.error('uploadUserLeave', error);
                }
            }
        }
        return await Leave.insertMany(validLeaves);
    }

    static normalizeLeaveType(rawLeaveType) {
        /** 'g' for global match */
        const matches = rawLeaveType.match(/Half Day/g);
        const count = matches ? matches.length : 0;
        /** If users applied multiple type of leaves for same day */
        if (count > 1) {
            if (rawLeaveType.includes(WORK_FROM_HOME_STRING)) {
                const cleanedLeaveType = rawLeaveType
                    .replace(/Work From Home\s*/g, "")        /** remove "Work From Home" and any trailing spaces */
                    .replace(/\s*\(Half Day\)/g, "")          /** remove "(Half Day)" with optional leading space */
                    .replace(/\s+/g, " ")                     /** normalize multiple spaces */
                    .trim();  
                return [{ cleanedType: cleanedLeaveType, duration: "first" }]
            }
            /** Regular expression to extract leave types */
            let leaveTypes = rawLeaveType.match(/([a-zA-Z\s]+)(?=\(Half Day\))/g);

            /** Remove extra spaces and return the leave types */
            let cleanedLeaveTypes = leaveTypes.map(type => type.trim());
            
            return cleanedLeaveTypes.map((leave, index) => ({ cleanedType: leave, duration: index === 0 ? "first" : "second" }))
        }
        const LEAVE_TYPE_SET = new Set(LEAVE_TYPE.map(type => type.trim()));
        let duration = 'full';
        if (rawLeaveType.includes(HALF_DAY_STRING)) {
            duration = 'first';
            rawLeaveType = rawLeaveType.replace(HALF_DAY_STRING, '');
        }
        let cleanedType = rawLeaveType.trim();
        const nestedMatch = cleanedType.match(/\(([^()]+)\)/g);
        if (nestedMatch) {
            const inner = nestedMatch[nestedMatch.length - 1].replace(/[()]/g, '').trim();
            if (LEAVE_TYPE_SET.has(inner)) {
                cleanedType = inner;
            }
        }
        if (!LEAVE_TYPE_SET.has(cleanedType)) {
            cleanedType = cleanedType.trim();
        }
        return [{ cleanedType, duration }]
    }

    static getFileParsedData(buffer, locale) {
        const worksheets = xlsx.parse(buffer, { raw: true });
        const returnPattern = {
            employeeIds: [],
            totalCount: 0,
            leaveInputArray: []
        };
        for (let xIndex = 0; xIndex < worksheets.length; xIndex++) {
            const worksheet = worksheets[xIndex];
            const rows = worksheet.data;
            const header = worksheet.data.shift();
            const dates = header.splice(5, header.length).map((date, index) => {
                if (!UploadUserLeaveService.isDateFormatValid(date)) {
                    throw new GeneralError(locale('INVALID_DATE_FORMAT', index + 5), 400);
                }
                return moment(date, 'DD-MMM-YYYY');
            });
            if (!rows.length) {
                return returnPattern;
            }
            for (let yIndex = 0; yIndex < rows.length; yIndex++) {
                const row = rows[yIndex];
                if (!row.length) {
                    return returnPattern;
                }
                returnPattern.totalCount++;
                returnPattern.employeeIds.push(row[2]);
                for (let zIndex = 0; zIndex < dates.length; zIndex++) {
                    const leaveDate = dates[zIndex];
                    let leaveTypes = this.normalizeLeaveType(row[5 + zIndex]);
                    leaveTypes.map(leaveType => {
                        if (LEAVE_TYPE.includes(leaveType.cleanedType?.trim())) {
                            returnPattern.leaveInputArray.push({
                                employeeId: row[2],
                                timeSpentHours: Utils.getTimeSpentHours(leaveType.duration),
                                leaveType: (leaveType.cleanedType !== 'H') ? leaveType.cleanedType : 'Holiday',
                                leaveDate,
                                duration: leaveType.duration
                            });
                        }
                    })
                }
            }
        }
        return returnPattern;
    }

    /**
     * @desc This function is being used to validate date format for leave data
     * <AUTHOR>
     * @since 23/08/2023
     * @param {String} inputDate
     */
    static isDateFormatValid(inputDate) {
        const parsedDate = moment(inputDate, 'DD-MMM-YYYY', true);
        if (parsedDate.isValid() && parsedDate.format('DD-MMM-YYYY') === inputDate) {
            return true;
        }
        return false;
    }
}
module.exports = UploadUserLeaveService;
