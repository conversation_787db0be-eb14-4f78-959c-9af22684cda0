/* eslint-disable max-len */
const ProjectTrackerHealthCardValidator = require('./projectTrackerHealthCardValidator');
const ProjectHealthCard = require('../../models/projectHealthCard.model');
const Epic = require('../../models/epic.model');
const Story = require('../../models/story.model');
const ProjectBoard = require('../../models/projectBoard.model');
const Project = require('../../models/project.model');
const mongoose = require('mongoose');
const axios = require('axios');
const constants = require('../../util/constants');

/**
 * Class represents services for Get Project Tracker Health Card Data.
 */
class ProjectTrackerHealthCardService {

    /**
     * @desc This function is being used to get the Project Trakcer List
     * <AUTHOR>
     * @since 27/11/2023
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async getProjectTrackerHealthCard (req, locale) {
        const projectId = req.query.projectId;
        const boardId = req.query.boardId;
        const Validator = new ProjectTrackerHealthCardValidator(req.body, locale);
        Validator.validate(boardId);

        // Caching logic: check if health card exists for this projectId and boardId
        const cached = await ProjectHealthCard.findOne({ projectId, boardId });
        if (cached) {
            return {
                epicEffortsInformation: cached.epicEffortsInformation,
                countInformation: cached.countInformation
            };
        }
        console.log('cache miss');
        console.log(`boardId: ${boardId} ProjectId: ${projectId}`);
        return await this.getProjectTrackerHealthCardCalculationAndCaching(boardId, projectId);
    }

    static async getProjectTrackerHealthCardCalculationAndCaching (boardId, projectId) {
        const epicSalesEstimate = await Epic.aggregate(this.getAggregateParamsForSalesEstimate(boardId));
        const storyLoggedEfforts = await Epic.aggregate(this.getAggregateParamsForLoggedEfforts(boardId));
        const epicInProgress = await Epic.aggregate(this.getAggregateParamsForEpicsInProgressV2(boardId));
        const storiesWithExtremeSubtaskCounts = await Story.aggregate(this.getStoriesWithExtremeSubtaskCounts(boardId));
        const jiraStoryNos = storiesWithExtremeSubtaskCounts.map(item => `'${item.jiraStoryNo}'`).join(', ');
        const countEC = {
            count: storiesWithExtremeSubtaskCounts.length,
            jiraStoryNos
        };
        const jiraStoryUrl = await Story.aggregate(this.getJiraStoryUrl(boardId));
        storyLoggedEfforts.forEach((item) => {
            if (item.status === constants.EPIC_STATUS.IN_PROGRESS) {
                item.toBeLoggedEfforts = epicInProgress[0].totalToBeLoggedEfforts;
            }
        });
        let totalEpicCount = 0;
        let totalStoryCount = 0;
        let totalSalesEstimate = 0;
        let totalDevSpent = 0;
        let totalDifference = 0;
        let totalDevToBeSpent = 0;


        const getDifference = (status) => {
            if (status === constants.EPIC_STATUS.TO_DO) {
                return getSalesEstimate(status) - getTotalLoggedEfforts(status) - getSalesEstimate(status);
            } else {
                return getSalesEstimate(status) - getTotalLoggedEfforts(status) - getToBeLoggedEfforts(status);
            }
        };

        const getEpicCount = (status) => {
            const match = epicSalesEstimate.find(item => item.status === status);
            return match ? match.epicCount : 0;
        };

        const getSalesEstimate = (status) => {
            const match = epicSalesEstimate.find(item => item.status === status);
            return match ? match.totalSalesEstimates : 0;
        };

        const getStoryCount = (status) => {
            const match = storyLoggedEfforts.find(item => item.status === status);
            return match ? match.storyCount : 0;
        };

        const getTotalLoggedEfforts = (status) => {
            const match = storyLoggedEfforts.find(item => item.status === status);
            return match ? match.totalLoggedEfforts : 0;
        };

        const getToBeLoggedEfforts = (status) => {
            const match = storyLoggedEfforts.find(item => item.status === status);
            return match ? match.toBeLoggedEfforts : 0;
        };

        const getIndividualToBeLoggedEfforts = (item) => {
            if (constants.EPIC_STATUS[item] !== constants.EPIC_STATUS.TO_DO) {
                return getToBeLoggedEfforts(constants.EPIC_STATUS[item]);
            } else {
                return getSalesEstimate(constants.EPIC_STATUS[item]);
            }
        };

        const getTotalSpent = () => totalDevSpent + totalDevToBeSpent;

        const resultArray = Object.keys(constants.EPIC_STATUS).map(item => {
            totalEpicCount += getEpicCount(constants.EPIC_STATUS[item]);
            totalSalesEstimate += getSalesEstimate(constants.EPIC_STATUS[item]);
            totalStoryCount += getStoryCount(constants.EPIC_STATUS[item]);
            totalDevSpent += getTotalLoggedEfforts(constants.EPIC_STATUS[item]);
            totalDevToBeSpent += getIndividualToBeLoggedEfforts(item);
            totalDifference += getSalesEstimate(constants.EPIC_STATUS[item]) - getTotalLoggedEfforts(constants.EPIC_STATUS[item]) - getIndividualToBeLoggedEfforts(item);
            return {
                storyCount: getStoryCount(constants.EPIC_STATUS[item]),
                status: constants.EPIC_STATUS[item],
                totalSalesEstimates: getSalesEstimate(constants.EPIC_STATUS[item])?.toFixed(2),
                totalLoggedEfforts: getTotalLoggedEfforts(constants.EPIC_STATUS[item])?.toFixed(2),
                toBeLoggedEfforts: getIndividualToBeLoggedEfforts(item)?.toFixed(2),
                difference: getDifference(constants.EPIC_STATUS[item])?.toFixed(2),
                epicCount: getEpicCount(constants.EPIC_STATUS[item])
            };
        });

        const { storiesWithoutEpics, bugsWithoutLinkedStories, subTaskCounts, storyWithOriginalEstimateGreaterThan40 } = await this.getIssuesWithoutEpic(boardId);
        const epicWithoutSE = await this.getEpicWithoutSalesEstimate(boardId);
        totalStoryCount = totalStoryCount + storiesWithoutEpics;

        const countInfo = {
            totalSpent: getTotalSpent()?.toFixed(2),
            totalSalesEstimate: totalSalesEstimate?.toFixed(2),
            totalDifference: totalDifference?.toFixed(2),
            totalDevSpent: totalDevSpent?.toFixed(2),
            totalDevToBeSpent: totalDevToBeSpent?.toFixed(2),
            totalEpicCount,
            totalStoryCount,
            storiesWithoutEpics,
            bugsWithoutLinkedStories,
            epicWithoutSE,
            storyWithOriginalEstimateGreaterThan40: storyWithOriginalEstimateGreaterThan40,
            storiesWithExtremeSubtaskCounts: countEC,
            subtasksCount: subTaskCounts,
            jiraStoryUrl: jiraStoryUrl[0] ? jiraStoryUrl[0]?.jiraStoryUrl?.split('/browse')[0] : 'https://growexx.atlassian.net/'
        };

        // Cache the calculated result for future requests
        if (projectId && boardId) {
            try {
                await ProjectHealthCard.findOneAndUpdate(
                    { projectId, boardId },
                    { projectId, boardId, epicEffortsInformation: resultArray, countInformation: countInfo },
                    { upsert: true, new: true }
                );
            } catch (err) {
                console.error('Error caching ProjectHealthCard:', err);
            }
        }
        return { epicEffortsInformation: resultArray, countInformation: countInfo };
    }

    static getAggregateParamsForSalesEstimate (boardId) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: 1,
                    salesEstimate: 1,
                    stories: {
                        // If stories array is null, replace with an empty array
                        $ifNull: ['$stories', []]
                    }
                }
            },
            {
                $addFields: {
                    epicStatus: {
                        $cond: {
                            if: {
                                $eq: [
                                    { $size: '$stories' },
                                    0
                                ]
                            },
                            then: constants.EPIC_STATUS.TO_DO,
                            else: {
                                $cond: {
                                    if: {
                                        $eq: [
                                            {
                                                $size: {
                                                    $setDifference: ['$stories.status', [constants.EPIC_STATUS.TO_DO]]
                                                }
                                            },
                                            0
                                        ]
                                    },
                                    then: constants.EPIC_STATUS.TO_DO,
                                    else: {
                                        $cond: {
                                            if: {
                                                $in: [constants.EPIC_STATUS.IN_PROGRESS, { $setUnion: ['$stories.status', []] }]
                                            },
                                            then: constants.EPIC_STATUS.IN_PROGRESS,
                                            else: {
                                                $cond: {
                                                    if: {
                                                        $eq: [
                                                            {
                                                                $size: {
                                                                    $setDifference: ['$stories.status', [constants.EPIC_STATUS.DONE]]
                                                                }
                                                            },
                                                            0
                                                        ]
                                                    },
                                                    then: constants.EPIC_STATUS.DONE,
                                                    else: constants.EPIC_STATUS.IN_PROGRESS
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $group: {
                    _id: '$epicStatus',
                    totalSalesEstimates: { $sum: '$salesEstimate' },
                    epicCount: { $sum: 1 }
                }
            },
            {
                $addFields: {
                    salesEstimate: { $divide: ['$totalSalesEstimates', 3600] }
                }
            },
            {
                $project: {
                    _id: 0,
                    status: '$_id',
                    totalSalesEstimates: { $round: ['$salesEstimate', 2] },
                    epicCount: 1
                }
            }
        ];
    }

    static getAggregateParamsForLoggedEfforts (boardId) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: 1,
                    stories: {
                        // If stories array is null, replace with an empty array
                        $ifNull: ['$stories', []]
                    }
                }
            },
            {
                $addFields: {
                    epicStatus: {
                        $cond: {
                            if: {
                                $eq: [
                                    { $size: '$stories' },
                                    0
                                ]
                            },
                            then: constants.EPIC_STATUS.TO_DO,
                            else: {
                                $cond: {
                                    if: {
                                        $eq: [
                                            {
                                                $size: {
                                                    $setDifference: ['$stories.status', [constants.EPIC_STATUS.TO_DO]]
                                                }
                                            },
                                            0
                                        ]
                                    },
                                    then: constants.EPIC_STATUS.TO_DO,
                                    else: {
                                        $cond: {
                                            if: {
                                                $in: [constants.EPIC_STATUS.IN_PROGRESS, { $setUnion: ['$stories.status', []] }]
                                            },
                                            then: constants.EPIC_STATUS.IN_PROGRESS,
                                            else: {
                                                $cond: {
                                                    if: {
                                                        $eq: [
                                                            {
                                                                $size: {
                                                                    $setDifference: ['$stories.status', [constants.EPIC_STATUS.DONE]]
                                                                }
                                                            },
                                                            0
                                                        ]
                                                    },
                                                    then: constants.EPIC_STATUS.DONE,
                                                    else: constants.EPIC_STATUS.IN_PROGRESS
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $unwind: {
                    path: '$stories'
                }
            },
            {
                $match: {
                    $or: [
                        { 'stories.issueType': constants.ISSUE_TYPE.STORY },
                        { 'stories.linkedStoryId': { $exists: true, $ne: null } }
                    ]
                }
            },
            {
                $group: {
                    _id: '$epicStatus',
                    stories: { $push: '$stories' },
                    totalLoggedEfforts: {
                        $sum: '$stories.loggedEffort'
                    },
                    storyCount: {
                        $sum: {
                            $cond: [
                                { $eq: ['$stories.issueType', constants.ISSUE_TYPE.STORY] },
                                1,
                                0
                            ]
                        }
                    }
                }
            },
            {
                $addFields: {
                    loggedEfforts: {
                        $divide: ['$totalLoggedEfforts', 3600]
                    },
                    toBeLoggedEfforts: {
                        $cond: {
                            if: {
                                $eq: ['$_id', constants.EPIC_STATUS.DONE]
                            },
                            then: 0,
                            else: {
                                $cond: {
                                    if: {
                                        $eq: ['$_id', constants.EPIC_STATUS.TO_DO]
                                    },
                                    then: {
                                        $sum: '$stories.originalEstimate'
                                    },
                                    else: {
                                        $sum: {
                                            $map: {
                                                input: '$stories',
                                                as: 'story',
                                                in: {
                                                    $cond: {
                                                        if: {
                                                            $ne: ['$$story.status', constants.EPIC_STATUS.DONE]
                                                        },
                                                        then: {
                                                            $cond: {
                                                                if: {
                                                                    $gte: [
                                                                        {
                                                                            $subtract: [
                                                                                '$$story.originalEstimate',
                                                                                '$$story.loggedEffort'
                                                                            ]
                                                                        },
                                                                        0
                                                                    ]
                                                                },
                                                                then: {
                                                                    $subtract: [
                                                                        '$$story.originalEstimate',
                                                                        '$$story.loggedEffort'
                                                                    ]
                                                                },
                                                                else: 0
                                                            }
                                                        },
                                                        else: 0
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    _id: 0,
                    status: '$_id',
                    toBeLoggedEfforts: { $round: [{ $divide: ['$toBeLoggedEfforts', 3600] }, 2] },
                    totalLoggedEfforts: { $round: ['$loggedEfforts', 2] },
                    storyCount: 1
                }
            }
        ];
    }

    static getAggregateParamsForEpicsInProgress (boardId) {
        return [
            { $match: { boardId: mongoose.Types.ObjectId(boardId) } },
            { $lookup: { from: 'stories', localField: '_id', foreignField: 'epicId', as: 'stories' } },
            {
                $addFields: {
                    epicStatus: {
                        $cond: {
                            if: { $eq: [{ $size: '$stories' }, 0] },
                            then: constants.EPIC_STATUS.TO_DO,
                            else: {
                                $cond: {
                                    if: { $in: [constants.EPIC_STATUS.IN_PROGRESS, '$stories.status'] },
                                    then: constants.EPIC_STATUS.IN_PROGRESS,
                                    else: {
                                        $cond: {
                                            if: { $eq: [{ $size: { $setDifference: ['$stories.status', [constants.EPIC_STATUS.DONE]] } }, 0] },
                                            then: constants.EPIC_STATUS.DONE,
                                            else: constants.EPIC_STATUS.IN_PROGRESS
                                        }
                                    }
                                }
                            }
                        }
                    },
                    storyCount: { $size: '$stories' }
                }
            },
            {
                $match: {
                    $and: [
                        { epicStatus: constants.EPIC_STATUS.IN_PROGRESS },
                        {
                            $or: [
                                { 'stories.issueType': constants.ISSUE_TYPE.STORY },
                                { 'stories.issueType': constants.ISSUE_TYPE.BUG }
                            ]
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: 1,
                    salesEstimate: 1,
                    stories: 1,
                    storyCount: 1
                }
            },
            {
                $addFields: {
                    toBeLoggedEfforts: {
                        $let: {
                            vars: {
                                toDoOriginalEstimates: {
                                    $sum: {
                                        $map: {
                                            input: '$stories',
                                            as: 'story',
                                            in: {
                                                $cond: [
                                                    { $eq: ['$$story.status', constants.EPIC_STATUS.TO_DO] },
                                                    '$$story.originalEstimate',
                                                    0
                                                ]
                                            }
                                        }
                                    }
                                },
                                remainingEfforts: {
                                    $subtract: [
                                        '$salesEstimate',
                                        {
                                            $sum: {
                                                $map: {
                                                    input: '$stories',
                                                    as: 'story',
                                                    in: {
                                                        $cond: [
                                                            { $ne: ['$$story.status', constants.EPIC_STATUS.TO_DO] },
                                                            '$$story.originalEstimate',
                                                            0
                                                        ]
                                                    }
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            in: {
                                $let: {
                                    vars: {
                                        greaterValue: {
                                            $max: ['$$toDoOriginalEstimates', '$$remainingEfforts']
                                        }
                                    },
                                    in: {

                                        $cond: {
                                            if: {
                                                $gt: [
                                                    {
                                                        $sum: {
                                                            $map: {
                                                                input: '$stories',
                                                                as: 'story',
                                                                in: {
                                                                    $cond: {
                                                                        if: { $eq: ['$$story.status', constants.EPIC_STATUS.IN_PROGRESS] },
                                                                        then: { $subtract: ['$$story.originalEstimate', '$$story.loggedEffort'] },
                                                                        else: 0
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    0
                                                ]
                                            },
                                            then: {
                                                $add: [
                                                    '$$greaterValue',
                                                    {
                                                        $sum: {
                                                            $map: {
                                                                input: '$stories',
                                                                as: 'story',
                                                                in: {
                                                                    $cond: {
                                                                        if: { $eq: ['$$story.status', constants.EPIC_STATUS.IN_PROGRESS] },
                                                                        then: {
                                                                            $subtract: [
                                                                                { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                { $ifNull: ['$$story.loggedEffort', 0] }
                                                                            ]
                                                                        },
                                                                        else: 0
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    },
                                                    {
                                                        $sum: {
                                                            $map: {
                                                                input: '$stories',
                                                                as: 'story',
                                                                in: {
                                                                    $cond: {
                                                                        if: { $eq: ['$$story.issueType', constants.ISSUE_TYPE.BUG] },
                                                                        then: {
                                                                            $subtract: [
                                                                                { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                { $ifNull: ['$$story.loggedEffort', 0] }
                                                                            ]
                                                                        },
                                                                        else: 0
                                                                    }
                                                                }
                                                            }
                                                        }
                                                    }
                                                ]
                                            },
                                            else: '$$greaterValue'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    toBeLoggedEfforts: { $round: [{ $divide: ['$toBeLoggedEfforts', 3600] }, 2] },
                    _id: 1,
                    name: 1,
                    storyCount: 1
                }
            },
            {
                $group: { _id: null, totalToBeLoggedEfforts: { $sum: '$toBeLoggedEfforts' }, epics: { $push: '$$ROOT' } }
            },
            { $project: { _id: 0, totalToBeLoggedEfforts: 1, epics: 1 } }
        ];
    }
    static getAggregateParamsForEpicsInProgressV2 (boardId) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardId)
                }
            },
            {
                $lookup: {
                    from: 'stories',
                    localField: '_id',
                    foreignField: 'epicId',
                    as: 'stories'
                }
            },
            {
                $addFields: {
                    epicStatus: {
                        $cond: {
                            if: { $eq: [{ $size: '$stories' }, 0] },
                            then: 'To Do',
                            else: {
                                $cond: {
                                    if: { $in: ['In Progress', '$stories.status'] },
                                    then: 'In Progress',
                                    else: {
                                        $cond: {
                                            if: {
                                                $eq: [
                                                    { $size: { $setDifference: ['$stories.status', ['Done']] } },
                                                    0
                                                ]
                                            },
                                            then: 'Done',
                                            else: 'In Progress'
                                        }
                                    }
                                }
                            }
                        }
                    },
                    storyCount: { $size: '$stories' }
                }
            },
            {
                $match: {
                    $and: [
                        { epicStatus: 'In Progress' },
                        {
                            $or: [
                                { 'stories.issueType': 'Story' },
                                { 'stories.issueType': 'Bug' }
                            ]
                        }
                    ]
                }
            },
            {
                $project: {
                    _id: 1,
                    epicName: 1,
                    salesEstimate: 1,
                    stories: 1,
                    storyCount: 1
                }
            },
            {
                $addFields: {
                    toBeLoggedEfforts: {
                        $let: {
                            vars: {
                                toDoOriginalEstimates: {
                                    $sum: {
                                        $map: {
                                            input: '$stories',
                                            as: 'story',
                                            in: {
                                                $cond: [
                                                    { $eq: ['$$story.status', 'To Do'] },
                                                    '$$story.originalEstimate',
                                                    0
                                                ]
                                            }
                                        }
                                    }
                                },
                                remainingEfforts: {
                                    $subtract: [
                                        '$salesEstimate',
                                        {
                                            $sum: {
                                                $map: {
                                                    input: '$stories',
                                                    as: 'story',
                                                    in: {
                                                        $cond: [
                                                            { $ne: ['$$story.status', 'To Do'] },
                                                            '$$story.originalEstimate',
                                                            0
                                                        ]
                                                    }
                                                }
                                            }
                                        }
                                    ]
                                }
                            },
                            in: {
                                $let: {
                                    vars: {
                                        greaterValue: {
                                            $max: ['$$toDoOriginalEstimates', '$$remainingEfforts']
                                        }
                                    },
                                    in: {
                                        $cond: {
                                            if: {
                                                $gt: [
                                                    {
                                                        $sum: {
                                                            $map: {
                                                                input: '$stories',
                                                                as: 'story',
                                                                in: {
                                                                    $cond: [
                                                                        { $eq: ['$$story.status', 'In Progress'] },
                                                                        {
                                                                            $cond: {
                                                                                if: {
                                                                                    $gt: [
                                                                                        { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                        { $ifNull: ['$$story.loggedEffort', 0] }
                                                                                    ]
                                                                                },
                                                                                then: {
                                                                                    $subtract: [
                                                                                        { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                        { $ifNull: ['$$story.loggedEffort', 0] }
                                                                                    ]
                                                                                },
                                                                                else: 0
                                                                            }
                                                                        },
                                                                        0
                                                                    ]
                                                                }
                                                            }
                                                        }
                                                    },
                                                    0
                                                ]
                                            },
                                            then: {
                                                $add: [
                                                    '$$greaterValue',
                                                    {
                                                        $sum: {
                                                            $map: {
                                                                input: '$stories',
                                                                as: 'story',
                                                                in: {
                                                                    $cond: [
                                                                        { $eq: ['$$story.status', 'In Progress'] },
                                                                        {
                                                                            $cond: {
                                                                                if: {
                                                                                    $gt: [
                                                                                        { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                        { $ifNull: ['$$story.loggedEffort', 0] }
                                                                                    ]
                                                                                },
                                                                                then: {
                                                                                    $subtract: [
                                                                                        { $ifNull: ['$$story.originalEstimate', 0] },
                                                                                        { $ifNull: ['$$story.loggedEffort', 0] }
                                                                                    ]
                                                                                },
                                                                                else: 0
                                                                            }
                                                                        },
                                                                        0
                                                                    ]
                                                                }
                                                            }
                                                        }
                                                    }
                                                ]
                                            },
                                            else: '$$greaterValue'
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            },
            {
                $project: {
                    toBeLoggedEfforts: {
                        $round: [
                            { $divide: ['$toBeLoggedEfforts', 3600] },
                            2
                        ]
                    },
                    _id: 1,
                    name: 1,
                    storyCount: 1
                }
            },
            {
                $group: {
                    _id: null,
                    totalToBeLoggedEfforts: { $sum: '$toBeLoggedEfforts' },
                    epics: { $push: '$$ROOT' }
                }
            },
            {
                $project: {
                    _id: 0,
                    totalToBeLoggedEfforts: 1,
                    epics: 1
                }
            }
        ];

    }
    static getStoriesWithExtremeSubtaskCounts (boardIds) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardIds)
                }
            },
            {
                $addFields: {
                    subtaskCount: {
                        $cond: {
                            if: { $isArray: '$subtasks' },
                            then: { $size: '$subtasks' },
                            else: 0
                        }
                    }
                }
            },
            {
                $match: {
                    $or: [
                        { subtaskCount: { $gt: 20 } },
                        { subtaskCount: { $lt: 1 } }
                    ]
                }
            }
        ];
    }
    static getJiraStoryUrl (boardIds) {
        return [
            {
                $match: {
                    boardId: mongoose.Types.ObjectId(boardIds)
                }
            },
            {
                $project: {
                    _id: 0,
                    jiraStoryUrl: 1
                }
            },
            {
                $limit: 1
            }
        ];
    }
    static async getIssuesWithoutEpic (boardId) {
        const boardData = await ProjectBoard.findById(boardId).select('projectId');
        const projectData = await Project.findById(boardData.projectId).populate('portalId');
        const url = `https://${projectData.portalId.url}/rest/api/2/search?jql=project=${projectData.jiraProjectId} AND type in(story) AND "parent" is EMPTY`;
        const issueData = await this.fetchDataFromJira(url, projectData.portalId);
        const bugsUrl = `https://${projectData.portalId.url}/rest/api/2/search?jql=project=${projectData.jiraProjectId} AND type = Bug AND issueLinkType = EMPTY`;
        const subtaskCountUrl = `https://${projectData.portalId.url}/rest/api/2/search?jql=project=${projectData.jiraProjectId} AND issuetype = Sub-task AND originalEstimate > 1d`;
        const storyWithOriginalEstimateGreaterThan40Url = `https://${projectData.portalId.url}/rest/api/2/search?jql=project=${projectData.jiraProjectId} 
        AND issuetype = Story AND originalEstimate > 40h`;
        const bugsData = await this.fetchDataFromJira(
            bugsUrl,
            projectData.portalId
        );
        const subtaskData = await this.fetchDataFromJira(subtaskCountUrl, projectData.portalId);
        const storyWithOriginalEstimateGreaterThan40 = await this.fetchDataFromJira(storyWithOriginalEstimateGreaterThan40Url, projectData.portalId);
        return {
            storiesWithoutEpics: issueData.total,
            bugsWithoutLinkedStories: bugsData.total,
            subTaskCounts: subtaskData.total,
            storyWithOriginalEstimateGreaterThan40: storyWithOriginalEstimateGreaterThan40.total
        };
    }

    static async getEpicWithoutSalesEstimate (boardId) {
        return await Epic.find({
            $and: [
                { boardId },
                {
                    $or: [
                        { salesEstimate: 0 },
                        { salesEstimate: { $exists: false } }
                    ]
                }
            ]
        }).count();
    }

    static async fetchDataFromJira (url, portal) {
        const response = await axios.get(url, {
            headers: {
                Authorization: `Basic ${Buffer.from(
                    `${portal.email}:${portal.token}`
                ).toString('base64')}`,
                Accept: 'application/json',
                'Content-Type': 'application/json'
            }
        });
        if (!response) {
            return {
                data: {
                    expand: 'schema,names',
                    startAt: 0,
                    maxResults: 0,
                    total: 0,
                    issues: []
                }
            };
        }
        return response.data;
    }
}

module.exports = ProjectTrackerHealthCardService;
