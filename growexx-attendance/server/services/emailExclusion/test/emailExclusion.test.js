const { expect } = require('chai');
const sinon = require('sinon');
const EmailExclusion = require('../../../models/emailExclusion.model');
const EmailExclusionService = require('../emailExclusionService');

// Helper for testing async/await errors
const expectError = async (fn, errorMessage) => {
    try {
        await fn();
        return false;
    } catch (err) {
        if (errorMessage) {
            expect(err.message).to.include(errorMessage);
        }
        return true;
    }
};

// Test data factory
const createTestEmail = (email, isActive = true, addedBy = '<EMAIL>') => ({
    email,
    isActive,
    addedBy,
    createdAt: new Date(),
    ...(!isActive && { removedAt: new Date() })
});

describe('EmailExclusionService', () => {
    let sandbox;
    
    beforeEach(() => {
        sandbox = sinon.createSandbox();
    });
    
    afterEach(() => {
        sandbox.restore();
    });

    describe('getExcludedEmails', () => {
        it('should return array of excluded emails', async () => {
            const mockEmails = [
                { email: '<EMAIL>', toObject: () => ({ email: '<EMAIL>' }) },
                { email: '<EMAIL>', toObject: () => ({ email: '<EMAIL>' }) }
            ];
            
            const findStub = sandbox.stub(EmailExclusion, 'find').returns({
                select: sandbox.stub().resolves(mockEmails)
            });
            
            const result = await EmailExclusionService.getExcludedEmails();
            
            expect(findStub.calledOnce).to.be.true;
            expect(result).to.be.an('array');
            expect(result).to.have.length(2);
            expect(result).to.include('<EMAIL>');
            expect(result).to.include('<EMAIL>');
        });

        it('should handle errors', async () => {
            sandbox.stub(EmailExclusion, 'find').returns({
                select: sandbox.stub().rejects(new Error('DB Error'))
            });
            
            const errorThrown = await expectError(
                () => EmailExclusionService.getExcludedEmails(),
                'Failed to fetch excluded emails: DB Error'
            );
            expect(errorThrown).to.be.true;
        });
    });

    describe('addExcludedEmails', () => {
        let getExcludedEmailsStub;
        let insertManyStub;

        beforeEach(() => {
            getExcludedEmailsStub = sandbox.stub(EmailExclusionService, 'getExcludedEmails').resolves([]);
            insertManyStub = sandbox.stub(EmailExclusion, 'insertMany');
        });

        it('should add new emails to exclusions', async () => {
            const emails = ['<EMAIL>', '<EMAIL>'];
            
            insertManyStub.resolves([
                { email: '<EMAIL>' },
                { email: '<EMAIL>' }
            ]);
            
            const result = await EmailExclusionService.addExcludedEmails(emails);
            
            expect(insertManyStub.calledOnce).to.be.true;
            expect(result).to.deep.equal({
                added: 2,
                duplicates: 0
            });
        });

        it('should handle duplicates in input', async () => {
            const emails = ['<EMAIL>', '<EMAIL>', '<EMAIL>'];
            
            insertManyStub.resolves([
                { email: '<EMAIL>' },
                { email: '<EMAIL>' }
            ]);
            
            const result = await EmailExclusionService.addExcludedEmails(emails);
            
            expect(insertManyStub.calledOnce).to.be.true;
            expect(result).to.deep.equal({
                added: 2,
                duplicates: 1
            });
        });

        it('should skip existing emails', async () => {
            const emails = ['<EMAIL>', '<EMAIL>'];
            
            getExcludedEmailsStub.resolves(['<EMAIL>']);
            insertManyStub.resolves([{ email: '<EMAIL>' }]);
            
            const result = await EmailExclusionService.addExcludedEmails(emails);
            
            expect(insertManyStub.calledOnce).to.be.true;
            expect(result).to.deep.equal({
                added: 1,
                duplicates: 1
            });
        });

        it('should handle empty array', async () => {
            const result = await EmailExclusionService.addExcludedEmails([]);
            expect(result).to.deep.equal({
                added: 0,
                duplicates: 0
            });
        });
    });

    describe('removeExcludedEmail', () => {
        let deleteStub;

        beforeEach(() => {
            deleteStub = sandbox.stub(EmailExclusion, 'deleteOne');
        });

        it('should delete an email from exclusions', async () => {
            const email = '<EMAIL>';
            deleteStub.resolves({ deletedCount: 1 });
            
            const result = await EmailExclusionService.removeExcludedEmail(email);
            
            expect(deleteStub.calledOnce).to.be.true;
            expect(deleteStub.firstCall.args[0]).to.deep.equal({
                email: email.toLowerCase()
            });
            expect(result.deletedCount).to.equal(1);
        });

        it('should handle case insensitivity', async () => {
            const email = '<EMAIL>';
            deleteStub.resolves({ deletedCount: 1 });
            
            await EmailExclusionService.removeExcludedEmail(email);
            
            expect(deleteStub.calledOnce).to.be.true;
            expect(deleteStub.firstCall.args[0]).to.deep.equal({
                email: email.toLowerCase()
            });
        });

        it('should not throw error if email not found', async () => {
            deleteStub.resolves({ deletedCount: 0 });
            
            const result = await EmailExclusionService.removeExcludedEmail('<EMAIL>');
            expect(result.deletedCount).to.equal(0);
        });

        it('should handle invalid email format', async function() {
            this.timeout(5000); // Increase timeout for this test
            
            const errorThrown = await expectError(
                () => EmailExclusionService.removeExcludedEmail('invalid-email')
            );
            expect(errorThrown).to.be.true;
        });
    });
});
