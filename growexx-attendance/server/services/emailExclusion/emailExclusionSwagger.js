const message = require('../../locales/en');

module.exports = swaggerJson => {
    swaggerJson.components = {
        schemas: {
            EmailExclusion: {
                type: 'object',
                properties: {
                    email: {
                        type: 'string',
                        format: 'email',
                        description: 'Email address to exclude from notifications'
                    },
                    createdAt: {
                        type: 'string',
                        format: 'date-time',
                        description: 'Timestamp when the email was added to exclusions'
                    }
                }
            }
        }
    };

    swaggerJson.paths['/api/email-exclusion'] = {
        get: {
            tags: ['EmailExclusion'],
            security: [
                {
                    bearerAuth: []
                }
            ],
            summary: 'Get all excluded emails',
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        type: 'array',
                        items: {
                            type: 'string',
                            format: 'email'
                        }
                    },
                    examples: {
                        'application/json': [
                            '<EMAIL>',
                            '<EMAIL>'
                        ]
                    }
                }
            }
        },
        post: {
            tags: ['EmailExclusion'],
            security: [
                {
                    bearerAuth: []
                }
            ],
            summary: 'Add emails to exclusion list',
            parameters: [{
                in: 'body',
                name: 'body',
                required: true,
                schema: {
                    type: 'object',
                    required: ['emails'],
                    properties: {
                        emails: {
                            type: 'array',
                            items: {
                                type: 'string',
                                format: 'email'
                            },
                            example: ['<EMAIL>', '<EMAIL>'],
                            description: 'Array of email addresses to exclude from notifications'
                        }
                    }
                }
            }],
            responses: {
                200: {
                    description: 'Success',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    added: {
                                        type: 'number',
                                        description: 'Number of emails successfully added'
                                    },
                                    duplicates: {
                                        type: 'number',
                                        description: 'Number of duplicate emails found'
                                    }
                                }
                            }
                        }
                    }
                }
            }
        }
    };

    swaggerJson.paths['/api/email-exclusion/{email}'] = {
        delete: {
            tags: ['EmailExclusion'],
            security: [
                {
                    bearerAuth: []
                }
            ],
            summary: 'Remove email from exclusion list',
            parameters: [
                {
                    name: 'email',
                    in: 'path',
                    required: true,
                    type: 'string',
                    format: 'email',
                    description: 'Email address to remove from exclusions',
                    example: '<EMAIL>'
                }
            ],
            responses: {
                200: {
                    description: 'Success',
                    content: {
                        'application/json': {
                            schema: {
                                type: 'object',
                                properties: {
                                    success: {
                                        type: 'boolean',
                                        example: true
                                    }
                                }
                            }
                        }
                    }
                }
            },
            responses: {
                200: {
                    description: 'Success',
                    schema: {
                        type: 'object',
                        properties: {
                            success: {
                                type: 'boolean',
                                example: true
                            },
                            message: {
                                type: 'string',
                                example: 'Email removed from exclusions'
                            }
                        }
                    }
                }
            }
        }
    };

    return swaggerJson;
};
