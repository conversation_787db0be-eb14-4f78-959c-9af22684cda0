const EmailExclusionService = require('./emailExclusionService');
const EmailExclusionValidator = require('./emailExclusionValidator');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents controller for email exclusion
 */
class EmailExclusionController {
    /**
     * @desc This function is being used to get all excluded emails
     * <AUTHOR>
     * @since 26/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async getExcludedEmails (req, res) {
        try {
            const data = await EmailExclusionService.getExcludedEmails();
            Utils.sendResponse(null, data, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to add emails to exclusion list
     * <AUTHOR>
     * @since 26/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async addExcludedEmails (req, res) {
        try {
            const validator = new EmailExclusionValidator(req.body, req.__);
            validator.validateAddExcludedEmails();
            const { emails } = req.body;
            const result = await EmailExclusionService.addExcludedEmails(emails);
            
            let message = res.__('SUCCESS');
            if (result.duplicates > 0) {
                message = res.__('EMAIL_EXCLUSION_PARTIAL_SUCCESS', {
                    added: result.added,
                    duplicates: result.duplicates
                });
            }
            
            Utils.sendResponse(null, result, res, message);
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }

    /**
     * @desc This function is being used to remove email from exclusion list
     * <AUTHOR>
     * @since 26/06/2025
     * @param {Object} req Request
     * @param {Object} res Response
     */
    static async removeExcludedEmail (req, res) {
        try {
            const validator = new EmailExclusionValidator(req.params, req.__);
            validator.validateRemoveExcludedEmail();
            const { email } = req.params;
            await EmailExclusionService.removeExcludedEmail(email);
            Utils.sendResponse(null, { success: true }, res, res.__('SUCCESS'));
        } catch (error) {
            Utils.sendResponse(error, null, res, error.message);
        }
    }
}

module.exports = EmailExclusionController;
