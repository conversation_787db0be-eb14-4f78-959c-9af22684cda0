const validation = require('../../util/validation');
const GeneralError = require('../../util/GeneralError');

/**
 * Class represents validations for email exclusion
 */
class EmailExclusionValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate email for exclusion
     * <AUTHOR>
     * @since 26/06/2025
     * @param {string} email Email to validate
     * @param {string} field Field name for error message
     */
    validateEmail (email, field = 'Email') {
        if (!email) {
            throw new GeneralError(this.__(this.REQUIRED, field), 400);
        }

        if (!CONSTANTS.REGEX.EMAIL.test(email)) {
            throw new GeneralError(this.__(this.INVALID, field), 400);
        }
    }

    /**
     * @desc This function is being used to validate add excluded emails request
     * <AUTHOR>
     * @since 26/06/2025
     */
    validateAddExcludedEmails () {
        const { emails } = this.body;

        if (!Array.isArray(emails) || emails.length === 0) {
            throw new GeneralError(this.__(this.REQUIRED, 'emails array'), 400);
        }

        // Validate each email in the array
        emails.forEach((email, index) => {
            if (typeof email !== 'string' || !email.trim()) {
                throw new GeneralError(`Email at index ${index} is invalid`, 400);
            }
            this.validateEmail(email, `Email at index ${index}`);
        });
    }

    /**
     * @desc This function is being used to validate remove excluded email request
     * <AUTHOR>
     * @since 26/06/2025
     */
    validateRemoveExcludedEmail () {
        const { email } = this.body;
        this.validateEmail(email, 'Email');
    }
}

module.exports = EmailExclusionValidator;
