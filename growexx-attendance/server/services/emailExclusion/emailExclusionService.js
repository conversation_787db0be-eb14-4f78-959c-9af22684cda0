const EmailExclusion = require('../../models/emailExclusion.model');

// Simple email validation regex
const EMAIL_REGEX = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

/**
 * Class represents services for email exclusion
 */
class EmailExclusionService {
    /**
     * @desc This function is being used to get all excluded emails
     * <AUTHOR>
     * @since 26/06/2025
     * @returns {Promise<Array>} Array of excluded email addresses
     */
    static async getExcludedEmails() {
        try {
            const exclusions = await EmailExclusion.find({}).select('email');
            return exclusions.map(e => e.email);
        } catch (error) {
            throw new Error(`Failed to fetch excluded emails: ${error.message}`);
        }
    }

    /**
     * @desc This function is being used to add emails to exclusion list
     * <AUTHOR>
     * @since 26/06/2025
     * @param {Array<string>} emails Array of emails to exclude
     * @returns {Promise<Object>} Object with added emails count and duplicates
     */
    static async addExcludedEmails(emails) {
        try {
            if (!Array.isArray(emails)) {
                throw new Error('Emails must be an array');
            }

            if (emails.length === 0) {
                return { added: 0, duplicates: 0 };
            }

            // Validate all emails first
            const invalidEmails = emails.filter(email => !EMAIL_REGEX.test(email));
            if (invalidEmails.length > 0) {
                throw new Error(`Invalid email format: ${invalidEmails.join(', ')}`);
            }

            // Get all existing emails in lowercase for comparison
            const existingEmails = (await this.getExcludedEmails()).map(e => e.toLowerCase());
            
            // Filter out duplicates and already excluded emails
            const uniqueNewEmails = [...new Set(emails.map(e => e.toLowerCase()))]
                .filter(email => !existingEmails.includes(email));

            if (uniqueNewEmails.length === 0) {
                return { 
                    added: 0, 
                    duplicates: emails.length 
                };
            }


            // Insert all new emails in a single operation
            const result = await EmailExclusion.insertMany(
                uniqueNewEmails.map(email => ({ 
                    email: email.toLowerCase()
                })),
                { ordered: false }
            );

            return { 
                added: result.length, 
                duplicates: emails.length - uniqueNewEmails.length 
            };
        } catch (error) {
            throw new Error(`Failed to add emails to exclusions: ${error.message}`);
        }
    }

    /**
     * @desc This function is being used to remove email from exclusion list
     * <AUTHOR>
     * @since 26/06/2025
     * @param {string} email Email to remove from exclusions
     * @returns {Promise<Object>} Deletion result
     */
    static async removeExcludedEmail(email) {
        try {
            if (!EMAIL_REGEX.test(email)) {
                throw new Error('Invalid email format');
            }

            const result = await EmailExclusion.deleteOne({ 
                email: email.toLowerCase() 
            });

            return result;
        } catch (error) {
            throw new Error(`Failed to remove email from exclusions: ${error.message}`);
        }
    }
}

module.exports = EmailExclusionService;
