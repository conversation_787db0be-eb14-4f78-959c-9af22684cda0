const AddUserLeaveValidator = require('./addLeaveValidator');
const Leave = require('../../models/leave.model');
const User = require('../../models/user.model');
const Utils = require('../../util/utilFunctions');

/**
 * Class represents services for add user leave.
 */
class AddUserLeaveService {

    /**
     * @desc This function is being used to add user leave
     * <AUTHOR>
     * @since 18/03/2021
     * @param {Object} req Request
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addUserLeave (req, locale) {
        let {
            startDate,
            endDate,
        } = req.body;

        const {
            employeeId,
            leaveType,
            startDateDuration,
            endDateDuration
        } = req.body;

        const Validator = new AddUserLeaveValidator(req.body, locale);
        Validator.validationAddUserLeave();
        const user = await User.findOne({
            employeeId
        }).select('_id');
        if (!user) {
            throw 'EMPLOYEE_NOT_FOUND';
        }
        const leaves = [];
        const leaveDay = MOMENT(startDate, 'YYYY-MM-DD');
        endDate = MOMENT(endDate, 'YYYY-MM-DD');
        startDate = MOMENT(startDate, 'YYYY-MM-DD');

        if (!startDate.isValid() || !endDate.isValid()) {
            throw 'INVALID_DATE';
        }

        while (leaveDay.isSameOrBefore(endDate)) {
            let duration;
            /** Single day leave */
            if (leaveDay.isSame(startDate, "day") && leaveDay.isSame(endDate, "day")) {
                duration = startDateDuration;
            } else if (leaveDay.isSame(startDate, 'day')) {
                duration = startDateDuration;
            } else if (leaveDay.isSame(endDate, 'day')) {
                duration = endDateDuration;
            } else {
                /** Middle days are full days. */
                duration = "full";
            }
            const leave = await this.addLeave(startDate, endDate, leaveDay, duration, user._id, leaveType);
            leaveDay.add(1, 'day');
            if (leave) {
                leaves.push(leave);
            }
        }
        
        return await Leave.insertMany(leaves);
    }

    static async addLeave (startDate, endDate, leaveDate, duration, userId, leaveType) {
        const isWeekend = leaveDate.weekday() % 6 === 0;
        if (isWeekend) {
            return null;
        }
        startDate = `${MOMENT(startDate, 'YYYY-MM-DD').format("YYYY-MM-DD")}T00:00:00.000+00:00`;
        endDate = `${MOMENT(endDate, 'YYYY-MM-DD').format("YYYY-MM-DD")}T00:00:00.000+00:00`
        leaveDate = `${MOMENT(leaveDate, 'YYYY-MM-DD').format("YYYY-MM-DD")}T00:00:00.000+00:00`;
        return new Leave({
            userId,
            leaveDate,
            startDate,
            endDate,
            leaveType,
            duration,
            timeSpentHours: Utils.getTimeSpentHours(duration)
        });
    }

}

module.exports = AddUserLeaveService;
