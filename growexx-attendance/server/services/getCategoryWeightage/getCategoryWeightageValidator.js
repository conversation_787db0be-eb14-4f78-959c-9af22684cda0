const validation = require('../../util/validation');

/**
 * Class represents validations for category weightage.
 */
class GetCategoryWeightageValidator extends validation {
    constructor (query, locale) {
        super(locale);
        this.query = query;
    }

    /**
     * @desc This function is being used to validate add a category weightage
     * <AUTHOR>
     * @since 24/08/2022
     */
    async validationGetCategoryWeightage () {
        const { designation, quarter, year } = this.query;
        await super.designation(designation, 'designation');
        super.quarter(quarter, 'quarter');
        super.year(_.toInteger(year), 'year');
    }
}
module.exports = GetCategoryWeightageValidator;
