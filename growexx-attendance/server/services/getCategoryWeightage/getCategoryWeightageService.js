const CategoryWeightage = require('../../models/categoryWeightage.model');
const GetCategoryWeightageValidator = require('./getCategoryWeightageValidator');
/**
/**
 * Class represents services for category weightage
 */
class GetCategoryWeightageService {
    static async getCategoryWeightage (req, user, locale) {
        const Validator = new GetCategoryWeightageValidator(req.query, locale);
        await Validator.validationGetCategoryWeightage();
        const { designation, quarter } = req.query;
        const year = parseInt(req.query.year);
        const res = await CategoryWeightage.aggregate([{
            $match: {
                designation, quarter, year }
        }, {
            $group: {
                '_id': { designation: '$designation',
                    quarter: '$quarter',
                    year: '$year',
                    isWeightageAssignedToKra: '$isWeightageAssignedToKra'
                },
                categories: {
                    $push: {
                        category: '$category',
                        weightage: '$weightage'
                    }
                }
            }
        }, {
            $project: {
                designation: '$_id.designation',
                categories: 1,
                isWeightageAssignedToKra: '$_id.isWeightageAssignedToKra',
                _id: 0
            }
        }]);
        return { ...res[0] };
    }
}
module.exports = GetCategoryWeightageService ;
