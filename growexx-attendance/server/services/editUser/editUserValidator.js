const validation = require('../../util/validation');

/**
 * Class represents validations for Jira portal Edit.
 */
class EditUserValidator extends validation {
    constructor (body, locale) {
        super(locale);
        this.body = body;
    }

    /**
     * @desc This function is being used to validate Edit a user
     * <AUTHOR>
     * @since 06/04/2021
     */
    async validationEditUser () {
        const { id, firstName, lastName, label, dateOfJoining, designation, businessUnit, level } = this.body;
        super.checkMongoId(id, 'Id' );
        super.firstName(firstName, 'First name');
        super.lastName(lastName, 'Last name');
        super.label(label, 'Label');
        super.dateOfJoining(dateOfJoining, 'Date of joining');
        await super.designation(designation, 'Designation');
        super.businessUnit(businessUnit, 'Business Unit');
        super.userLevel(level);
    }

    /**
     * @desc This function is being used to validate Edit a user
     * <AUTHOR>
     * @since 06/04/2021`
     */
    validationEditUserRole () {
        const { id, role } = this.body;
        super.checkMongoId(id, 'Id');
        super.role(role, 'role');
    }
}

module.exports = EditUserValidator;
