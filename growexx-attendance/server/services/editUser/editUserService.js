const mongoose = require("mongoose");
const User = require("../../models/user.model");
const EditUserValidator = require("./editUserValidator");
const GeneralError = require("../../util/GeneralError");
const Crypt = require("../../util/crypt");

/**
 * Class represents services for Edit a new user.
 */
class EditUserService {
  /**
   * @desc This function is being used to edit a new user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @param {Object} locale Locale passed from request
   * @return {Object} response Success response
   */
  static async editUser(req, user, locale) {
    const Validator = new EditUserValidator(req.body, locale);
    await Validator.validationEditUser();
    const id = mongoose.Types.ObjectId(req.body.id);
    const dojDate = MOMENT(req.body.dateOfJoining, "YYYY-MM-DD");
    const {
      firstName,
      lastName,
      label,
      designation,
      password,
      level,
      businessUnit,
    } = req.body;
    const checkUser = await User.findOne(
      { _id: { $ne: id }, label },
      { _id: 1, label: 1 }
    );

    if (checkUser && _.intersection(checkUser.label, label).length) {
      throw new GeneralError(locale("LABEL_EXISTS"), 400);
    }

    const updateData = {
      firstName,
      lastName,
      label,
      designation,
      level,
      businessUnit,
      doj: dojDate,
    };

    if (password) {
      Validator.password(password);
      updateData.password = Crypt.enCryptPassword(password);
    }

    return await User.updateOne(
      { _id: id },
      {
        $set: updateData,
      }
    );
  }

  /**
   * @desc This function is being used to edit a new user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Object} req Request
   * @param {Object} user Logged in user details
   * @param {Object} locale Locale passed from request
   * @return {Object} response Success response
   */
  static async editUserRole(req, user, locale) {
    const Validator = new EditUserValidator(req.body, locale);
    Validator.validationEditUserRole();
    const id = mongoose.Types.ObjectId(req.body.id);

    const updateData = {
      role: req.body.role,
    };

    return await User.updateOne(
      { _id: id },
      {
        $set: updateData,
      }
    );
  }
}

module.exports = EditUserService;
