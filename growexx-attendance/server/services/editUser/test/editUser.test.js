const TestCase = require('./editUser');
const chai = require('chai');
const chaiHttp = require('chai-http');
const expect = chai.expect;
const assert = chai.assert;
const request = require('supertest');
chai.use(chaiHttp);
const jwt = require('jsonwebtoken');
const tokenOptionalInfo = {
    algorithm: 'HS256',
    expiresIn: 86400
};

// Admin Token
const user = {
    id: '5f5f2cd2f1472c3303b6b861',
    email: '<EMAIL>'
};
const requestPayloadUser = {
    token: jwt.sign(user, process.env.JWT_SECRET, tokenOptionalInfo)
};

describe('Edit users', () => {

    TestCase.editUser.forEach((data) => {
        it(data.it, async () => {
            const res = await request(process.env.BASE_URL)
                .put('/user')
                .set({ Authorization: requestPayloadUser.token })
                .send(data.options);
            expect(res.body.status).to.be.status;
            assert.equal(res.body.status, data.status);
        });
    });

    it('As a Admin, I should able to add user', async () => {
        const data = {
            id: '5f083c352a7908662c334532',
            password:
            '8776f108e247ab1e2b323042c049c266407c81fbad41bde1e8dfc1bb66fd267e',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label1'],
            dateOfJoining: '2021-09-04',
            designation: 'Software Engineer',
            level: 'L1',
            businessUnit: 'Web & Mobile'
        };
        const res = await request(process.env.BASE_URL)
            .put('/user')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to edit user', async () => {
        const data = {
            id: '5f083c352a7908662c334532',
            firstName: 'Test',
            lastName: 'Last',
            label: ['new.label1'],
            dateOfJoining: new Date('2021-10-04'),
            designation: 'Software Engineer',
            level: 'L1',
            businessUnit: 'Web & Mobile'
        };
        const res = await request(process.env.BASE_URL)
            .put('/user')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to validate user role', async () => {
        const data = {
            id: '5f083c352a7908662c334532',
            role: 0
        };
        const res = await request(process.env.BASE_URL)
            .patch('/user/role')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 400);
    });

    it('As a Admin, I should able to validate user role should not be admin', async () => {
        const data = {
            id: '5f083c352a7908662c334532',
            role: '4'
        };
        const res = await request(process.env.BASE_URL)
            .patch('/user/role')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });

    it('As a Admin, I should able to edit user role', async () => {
        const data = {
            id: '5f083c352a7908662c334532',
            role: '2'
        };
        const res = await request(process.env.BASE_URL)
            .patch('/user/role')
            .set({ Authorization: requestPayloadUser.token })
            .send(data);
        expect(res.body.status).to.be.status;
        assert.equal(res.statusCode, 200);
    });
});
