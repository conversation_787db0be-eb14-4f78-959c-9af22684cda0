const User = require('../../models/user.model');
const GeneralError = require('../../util/GeneralError');
const Crypt = require('../../util/crypt');
const Designation = require('../../models/designation.model');


/**
 * Class represents services for add a new user.
 */
class DesignationService {

    /**
     * @desc This function is being used to add a new user
     * <AUTHOR>
     * @since 06/04/2021
     * @param {Object} req Request
     * @param {Object} user Logged in user details
     * @param {Object} locale Locale passed from request
     * @return {Object} response Success response
     */
    static async addDesignation (req) {
        const { name } = req.body;

        if (!name) {
            throw new GeneralError('Name is required', 400);
        }

        // Convert to PascalCase while preserving spaces
        const pascalCaseName = this.toPascalCase(name);

        // Check for existing designation with a case-sensitive query
        const existingDesignation = await Designation.findOne({ name: pascalCaseName }).collation({
            locale: 'en',
            strength: 2
        });

        if (existingDesignation) {
            throw new GeneralError('Designation with this name already exists', 409);
        }

        // Create new Designation and save to the database
        const designation = new Designation({ name: pascalCaseName });
        await designation.save();

        return pascalCaseName;
    }

    static toPascalCase (str) {
        return str
            .replace(/\s+(.)/g, (match, group1) => ' ' + group1.toUpperCase())
            .replace(/^(.)/, (match, group1) => group1.toUpperCase());
    }


    /**
     * @desc Get all Designations with pagination
     * @route GET /designation
     */
    static async listDesignation () {
        const designations = await Designation.find({});
        const designationList = designations.map(designation => ({
            label: designation.name,
            value: designation.name,
            id: designation._id.toString() // Convert ObjectId to string for easier handling in the frontend
        }));
        return designationList;
    }

}

module.exports = DesignationService;
