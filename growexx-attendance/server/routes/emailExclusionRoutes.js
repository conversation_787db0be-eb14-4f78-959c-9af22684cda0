const router = require('express').Router();
const AuthMiddleWare = require('../middleware/auth');
const ACLMiddleWare = require('../middleware/acl');
const EmailExclusionController = require('../services/emailExclusion/emailExclusionController');

/**
 * @swagger
 * /api/email-exclusion:
 *   get:
 *     tags: [EmailExclusion]
 *     security:
 *       - BearerAuth: []
 *     summary: Get all excluded emails
 *     responses:
 *       200:
 *         description: Success
 */
router.get(
    '/',
    AuthMiddleWare,
    ACLMiddleWare,
    EmailExclusionController.getExcludedEmails
);

/**
 * @swagger
 * /api/email-exclusion:
 *   post:
 *     tags: [EmailExclusion]
 *     security:
 *       - BearerAuth: []
 *     summary: Add emails to exclusion list
 *     parameters:
 *       - in: body
 *         name: body
 *         required: true
 *         schema:
 *           type: object
 *           required:
 *             - emails
 *           properties:
 *             emails:
 *               type: array
 *               items:
 *                 type: string
 *                 format: email
 *     responses:
 *       200:
 *         description: Success
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 added:
 *                   type: number
 *                   description: Number of emails added
 *                 duplicates:
 *                   type: number
 *                   description: Number of duplicate emails found
 */
router.post(
    '/',
    AuthMiddleWare,
    ACLMiddleWare,
    EmailExclusionController.addExcludedEmails
);

/**
 * @swagger
 * /api/email-exclusion/{email}:
 *   delete:
 *     tags: [EmailExclusion]
 *     security:
 *       - BearerAuth: []
 *     summary: Remove email from exclusion list
 *     parameters:
 *       - in: path
 *         name: email
 *         required: true
 *         type: string
 *         format: email
 *     responses:
 *       200:
 *         description: Success
 */
router.delete(
    '/:email',
    AuthMiddleWare,
    ACLMiddleWare,
    EmailExclusionController.removeExcludedEmail
);

module.exports = router;
