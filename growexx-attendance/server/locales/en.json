{"SUCCESS": "Success", "SIGNIN_SUCCESS": "User successfully logged in", "FIELD_REQUIRED": "%s is required", "FIELD_NOT_VALID": "%s is not valid.", "INVALID_REQUEST": "Request is invalid", "CHANGE_PASSWORD_SUCCESS": "Your password has been changed", "PASSWORD_NOT_MATCH": "Your old password is not matched.", "LINK_IS_NOT_VALID": "Link is not valid. Please try again.", "LINK_IS_VALID": "Link is valid.", "RESET_LINK_EXPIRED": "Link is expired. Please try again.", "FORGOT_PASSWORD_LINK_SENT_SUCCESS": "Link has been sent to your email.", "RESET_PASSWORD_SUCCESS": "Password has been successfully reset.", "ERROR_MSG": "Something went wrong. please try again.", "ACCESS_DENIED": "You are not authorized to access this resource.", "DEACTIVATE_ACCOUNT_BY_ADMIN": "You account has been deactivate.", "PHOTO_DELETE_SUCCESS": "Your profile picture has been deleted successfully.", "PORTAL_EXISTS": "Jira portal is already exists", "INVALID_JIRA_CREDENTIALS": "The entered email and token are not correct. Please verify it.", "SELECT_EMPLOYEE": "Select the employee first", "INVALID_PORTAL_ID": "Jira portal id is invalid.", "LABEL_EXISTS": "Label is already exists", "EMAIL_EXISTS": "Email is already exists", "EMPLOYEE_ID_EXISTS": "Employee id already exists", "LOGIN_FAILED": "Entered credentials are not valid.", "USER_INACTIVE": "User is not active.", "PROJECT_EXISTS": "Project is already exists", "PROJECT_NOT_FOUND": "Project not found", "NOT_TEAM_MEMEBER": "You can not view attendace of this user becuase it is not your team member.", "NON_WORKING_DAY": "Leave not applicable for a non-working day", "UPLOAD_LEAVE_ERROR": "Leave upload error.", "EMPLOYEE_NOT_FOUND": "Employee not found", "INVALID_DATE": "Date must be in YYYY-MM-DD format", "KRA_NOT_EXISTS": "<PERSON><PERSON> is not found", "INVALID_KRA_FILE": "Please check the KRA data for designation and weightage", "USER_KRA_ID": "User KRA is not found. Please check with admin.", "PM_NOT_ELIGIBLE": "You are not able to fill the KRA for this employee for this project", "KRA_NOT_PUBLISHED": "This employee KRA is not yet published. Please wait or ask HR.", "KRA_NOT_FOUND_FOR_QUARTER": "KRA is not assigned to any employee for selected quarter", "INVALID_KRA_ID": "KRA id is invalid.", "DUPLICATE_EMPLOYEE_EXISTS": "You cannot add an employee more than once in a project.", "USER_FREEZE": "You have already freezed your review", "FILE_SIZE_INVALID": "File size is not valid for file: %s", "KRA_ATTACHMENT_FILE_COUNT_INVALID": "You cannot upload more than %s files for any given KRA", "FILE_NOT_FOUND": "File not found", "FUTURE_DATE_NOT_ALLOWED": "You are not allowed to add future dates", "ASSESSMENT_FREEZED": "You have already freezed your review", "EMPTY_FIELD": " You cannot pass an empty field", "RATINGS_ALREADY_RELEASED": "Ratings already released for applicable kras.", "FREEZED_BY_REVIEWER": "Review manager has already freezed the ratings", "KRA_ATTACHMENT_UPLOAD_SUCCESS": "KRA attachment uploaded successfully", "KRA_ASSIGNED": "KRA is assigned. Please go to self KRA menu to view your KRA.", "RATINGS_RELEASED": "Your rating has been released. Please go to self KRA menu to view your ratings.", "ASSESSMENT_STARTED": "Your assessment has been started. Please start add self comments.", "REPORTING_MANAGER_RATING": "User has added self comments. Please add your feedback.", "REVIEW_MANAGER_RATING": "Reporting manager has added their feedback. Please add your feedback.", "NOTIFICATION_NOT_FOUND": "There is no notification for this user.", "CATEGORY_WEIGHTAGE_MORE_THAN_HUNDRED": "catagory weightage is more than hundred. Please check it.", "UNFREEZE_REQUEST_APPROVED": "Unfreeze request is approved. Please add your feedback", "KRA_ASSIGNED_FOR_DESIGNATION": "KRA is assigned for selected designation.", "INVALID_QUARTER_ALL": "Quarter should be same for all the categories", "INVALID_YEAR_ALL": "Year should be same for all the categories", "INVALID_DESIGNATION_ALL": "Designation should be same for all the categories", "CATEGORY_MISSING": "Please provide all category data for selected designation.", "REVIEWER_MISSING": "Please add Review manager in project for assigning kra to reporting manager.", "CHAR_LIMIT_EXCEED": "%s should contains characters between 1 to 1000 in row number %s", "INVALID_DATE_FORMAT": "Invalid date format specified on %sth column. Please provide in DD-MMM-YYYY format.", "FILE_TYPE_NOT_VALID": "Please upload file in %s format.", "GET_TIME_SUCCESS": "Time fetched successfully", "MENTEES_FETCHED": "MENTEES_FETCHED", "MENTEES_ASSIGNED": "MENTEES_ASSIGNED", "INVALID_MONTH": "INVALID_MONTH", "INVALID_YEAR": "INVALID_YEAR", "INVALID_OBJECT_ID": "Invalid %s format", "TOTAL_WEIGHTAGE_HUNDRED": "Total weightage must equal 100%", "INVALID_PROJECT_WEIGHTAGE": "Project weightage must be between 0 and 100", "PLI_RATING_NOT_FOUND": "PLI_RATING_NOT_FOUND", "INVALID_PROJECT_TYPE": "INVALID_PROJECT_TYPE", "INVALID_EMPLOYEE_ID": "Employee ID is invalid", "INVALID_MENTEE_ID": "Invalid mentee ID format", "INVALID_MENTOR_ID": "Invalid mentor ID format", "USER_NOT_FOUND": "User not found %s", "MENTOR_MENTEE_ASSIGNMENT_PROCESSED": "Mentor-mentee assignments processed successfully", "FILE_NOT_PROVIDED": "File not provided", "FILE_TOO_LARGE": "File size exceeds the maximum limit", "INVALID_FILE_TYPE": "Invalid file type. Only JSON files are supported", "EMPTY_FILE": "The uploaded file is empty", "INSUFFICIENT_PERMISSIONS": "You do not have permission to perform this action", "MENTEE_IS_MENTOR_OF_MENTOR": "Cannot assign mentor as mentee creates a circular relationship", "MENTOR_CANNOT_BE_MENTEE": "A user cannot be both mentor and mentee for the same relationship", "INVALID_DATA_FORMAT": "Invalid data format. Expected an array of mentor-mentee assignments", "ERROR_IN_DATABASE": "ERROR_IN_DATABASE", "PLI_RATING_ID_REQUIRED": "PLI_RATING_ID_REQUIRED", "INCOMPLETE_PLI_PARAMETERS": "INCOMPLETE_PLI_PARAMETERS", "PLI_ALREADY_FROZEN_BY_SUPERADMIN": "PLI_ALREADY_FROZEN_BY_SUPERADMIN", "PLI_FROZEN": "PLI_FROZEN", "EMAIL_EXCLUSION_PARTIAL_SUCCESS": "EMAIL_EXCLUSION_PARTIAL_SUCCESS"}