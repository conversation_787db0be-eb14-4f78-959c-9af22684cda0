const mongoose = require('mongoose');
const GeneralError = require('../util/GeneralError');
const REQUIRED = 'FIELD_REQUIRED';
const INVALID = 'FIELD_NOT_VALID';
const {
    LEAVE_TYPE,
    DESIGNATION,
    KRA_CATAGORY,
    KRA_QUARTER,
    KRA_YEAR,
    BUSINESS_UNIT
} = require('../util/constants');
const designationModel = require('../models/designation.model');

/**
 * Created by Growexx on 04/06/2020
 * @name alidator
 */
class Validator {
    constructor (locale) {
        this.REQUIRED = REQUIRED;
        this.INVALID = INVALID;

        if (locale) {
            this.__ = locale;
        }
    }

    /**
   * @desc This function is being used to validate email address
   * <AUTHOR>
   * @since 01/03/2021
   * @param {string} email Email
   */
    email (email) {
        if (!email) {
            throw new GeneralError(this.__(REQUIRED, 'Email'), 400);
        }

        if (!CONSTANTS.REGEX.EMAIL.test(email)) {
            throw new GeneralError(this.__(INVALID, 'Email'), 400);
        }
    }

    /**
   * @desc This function is being used to check password
   * <AUTHOR>
   * @since 01/03/2021
   * @param {string} password Password
   */
    password (password) {
        if (!password) {
            throw new GeneralError(this.__(REQUIRED, 'Password'), 400);
        }

        if (password.length !== 64) {
            throw new GeneralError(this.__(INVALID, 'Password'), 400);
        }
    }

    /**
   *
   * @param {URL} URL
   * @param {string} field  Field Name
   */
    url (URL, field = 'Website') {
        if (!URL) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!CONSTANTS.REGEX.URL.test(URL)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate id is not blank
   * <AUTHOR>
   * @param {string} id id
   * @since 23/06/2020
   */
    checkMongoId (id, field = 'Id') {
        if (!id) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!mongoose.Types.ObjectId.isValid(id)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate firstName user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Number} firstName firstName
   */
    firstName (firstName, field) {
        if (!firstName) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate lastName user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Number} lastName lastName
   */
    lastName (lastName, field) {
        if (!lastName) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate label user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Number} label label
   */
    label (label, field) {
        if (!label || !label.length) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!Array.isArray(label)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate project name
   * <AUTHOR>
   * @since 28/04/2021
   * @param {Number} label projectName
   */
    projectName (projectName, field) {
        if (!projectName || !projectName.length) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate mongo id
   * <AUTHOR>
   * @param {string} id id
   * @since 23/06/2020
   */
    checkValidMongoId (id, field = 'Id') {
        if (!mongoose.Types.ObjectId.isValid(id)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate role user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {Number} role role
   */
    role (role, field) {
        if (!role) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!Number(role) || role === CONSTANTS.ROLE.ADMIN) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate firstName user
   * <AUTHOR>
   * @since 06/04/2021
   * @param {String} userId userId
   */
    userId (userId, field) {
        if (!userId) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate User leave add leaveType
   * <AUTHOR>
   * @since 18/03/2021
   * @param {Number} leaveType leaveType
   */
    leaveType (leaveType, field) {
        if (!leaveType) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (LEAVE_TYPE.indexOf(leaveType) === -1) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate User leave add leaveDate
   * <AUTHOR>
   * @since 18/03/2021
   * @param {Number} leaveDate leaveDate
   */
    leaveDate (leaveDate, field) {
        if (!leaveDate) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This function is being used to validate User leave add duration
   * <AUTHOR>
   * @since 18/03/2021
   * @param {Number} duration duration
   */
    duration (duration, field) {
        if (!duration) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!Number(duration) || duration <= 0) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate User date of joining
   * <AUTHOR>
   * @since 04/10/2021
   * @param {String} date date
   */

    dateOfJoining (date, field) {
        const testDate = MOMENT(date, 'YYYY-MM-DD');
        const newDate = MOMENT().format('YYYY-MM-DD');
        if (!date) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!testDate.isValid() || testDate > newDate) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate start date must not be greater than end date
   * <AUTHOR>
   * @since 07/07/2022
   * @param {String} startDate startDate
   * @param {String} endDate endDate
   */

    projectStartEndDate (startDate, endDate, field) {
        const projectStartDate = MOMENT(startDate, 'YYYY-MM-DD');
        const projectEndDate = MOMENT(endDate, 'YYYY-MM-DD');

        if (!startDate || !endDate) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (!projectStartDate.isBefore(projectEndDate)) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate start date must not be greater than end date
   * <AUTHOR>
   * @since 07/07/2022
   * @param {String} startDate startDate
   * @param {String} endDate endDate
   */

    startEndDate (startDate, endDate, field) {
        const projectStartDate = MOMENT(startDate, 'YYYY-MM-DD');
        const projectEndDate = MOMENT(endDate, 'YYYY-MM-DD');

        if (!startDate) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }

        if (projectEndDate && projectStartDate > projectEndDate) {
            throw new GeneralError(this.__(INVALID, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate start date of Users
   * <AUTHOR>
   * @since 08/08/2022
   * @param {String} startDate startDate
   * @param {String} endDate endDate
   */

    usersStartEndDate (users) {
        users.forEach((user) => {
            this.startEndDate(user.startDate, user.endDate, 'Start date & End Date');
        });
    }

    /**
   * @desc This function is being used to validate user level
   * <AUTHOR>
   * @since 16/08/2022
   * @param {String} userLevel userLevel
   */
    userLevel (userLevel) {
        if (!userLevel) {
            throw new GeneralError(this.__(REQUIRED, 'Level'), 400);
        }
        if (!CONSTANTS.LEVELS.includes(userLevel)) {
            throw new GeneralError(this.__(INVALID, 'Level'), 400);
        }
    }

    /**
   * @desc This funciton is used to validate reporting manager must be present
   * <AUTHOR>
   * @since 07/07/2022
   * @param {String} reportingManager reportingManager
   */

    projectReportingManger (reportingManager, field) {
        if (!reportingManager) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate review manager must be present
   * <AUTHOR>
   * @since 07/07/2022
   * @param {String} reviewManager reviewManager
   */

    projectReviewManger (reviewManager, field) {
        if (!reviewManager) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        }
    }

    /**
   * @desc This funciton is used to validate employees must be present
   * <AUTHOR>
   * @since 07/07/2022
   * @param {String} employees employees
   */

    async projectEmployees (employees, field) {
        const designations = await designationModel.find({});
        const designationsName = designations.map(
            (designation) => designation.name
        );
        if (!employees || employees.length === 0) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        } else {
            employees.forEach((employee) => {
                if (!designationsName.includes(employee.empRole)) {
                    throw new GeneralError(this.__(INVALID, 'Employee Role'), 400);
                }
            });
        }
    }
    /*
   * @desc This function is being used to validate category of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} category category
   */
    category (category) {
        if (!category) {
            throw new GeneralError(this.__(REQUIRED, 'category'), 400);
        }
        if (!_.isString(category)) {
            throw new GeneralError(this.__(INVALID, 'category'), 400);
        }
        if (!KRA_CATAGORY.includes(category)) {
            throw new GeneralError(this.__(INVALID, 'category'), 400);
        }
    }

    /**
   * @desc This function is being used to validate weightage of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} weightage weightage
   */
    weightage (weightage) {
        if (!weightage) {
            throw new GeneralError(this.__(REQUIRED, 'weightage'), 400);
        }
        if (isNaN(weightage)) {
            throw new GeneralError(this.__(INVALID, 'weightage'), 400);
        }
        if (
            weightage < CONSTANTS.WEIGHTAGE.MIN ||
      weightage > CONSTANTS.WEIGHTAGE.MAX
        ) {
            throw new GeneralError(this.__(INVALID, 'weightage'), 400);
        }
    }

    /**
   * @desc This function is being used to validate outcome of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} outcome outcome
   */
    outcome (outcome) {
        if (!outcome) {
            throw new GeneralError(this.__(REQUIRED, 'outcome'), 400);
        }
        if (!_.isString(outcome)) {
            throw new GeneralError(this.__(INVALID, 'outcome'), 400);
        }
        if (
            outcome.length < CONSTANTS.SIZE.MIN ||
      outcome.length > CONSTANTS.SIZE.MAX
        ) {
            throw new GeneralError(this.__(INVALID, 'outcome'), 400);
        }
    }

    /**
   * @desc This function is being used to validate measure of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} measure measure
   */
    measure (measure) {
        if (!measure) {
            throw new GeneralError(this.__(REQUIRED, 'measure'), 400);
        }
        if (!_.isString(measure)) {
            throw new GeneralError(this.__(INVALID, 'measure'), 400);
        }
    }

    /**
   * @desc This function is being used to validate target of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} target target
   */
    target (target) {
        if (!target) {
            throw new GeneralError(this.__(REQUIRED, 'target'), 400);
        }
        if (!_.isString(target)) {
            throw new GeneralError(this.__(INVALID, 'target'), 400);
        }
        if (
            target.length < CONSTANTS.SIZE.MIN ||
      target.length > CONSTANTS.SIZE.MAX
        ) {
            throw new GeneralError(this.__(INVALID, 'target'), 400);
        }
    }

    /**
   * @desc This function is being used to validate designation of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} designation designation
   */
    async designation (designation) {
        if (!designation) {
            throw new GeneralError(this.__(REQUIRED, 'designation'), 400);
        }
        const designationList = await designationModel.find({}).lean();
        const designationNames = designationList.map((d) => d.name);
        if (!designationNames.includes(designation)) {
            throw new GeneralError(this.__(INVALID, 'designation'), 400);
        }
    }
    /*
   * @desc This function is being used to validate quarter of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} category quarter
   */
    quarter (quarter) {
        if (!quarter) {
            throw new GeneralError(this.__(REQUIRED, 'quarter'), 400);
        }
        if (!_.isString(quarter)) {
            throw new GeneralError(this.__(INVALID, 'quarter'), 400);
        }
        if (!KRA_QUARTER.includes(quarter)) {
            throw new GeneralError(this.__(INVALID, 'quarter'), 400);
        }
    }
    /*
   * @desc This function is being used to validate year of KRA
   * <AUTHOR>
   * @since 07/07/2022
   * @param {string} category year
   */
    year (year) {
        if (!year) {
            throw new GeneralError(this.__(REQUIRED, 'year'), 400);
        }
        if (!KRA_YEAR.includes(year)) {
            throw new GeneralError(this.__(INVALID, 'year'), 400);
        }
    }
    /**
   * @desc This funciton is used to validate kras must be present
   * <AUTHOR>
   * @since 12/07/2022
   * @param {String} kras kras
   */

    userKras (kras, field) {
        if (!kras || kras.length === 0) {
            throw new GeneralError(this.__(REQUIRED, field), 400);
        } else {
            kras.forEach((kra) => {
                this.outcome(kra.outcome);
                this.category(kra.category);
                this.weightage(kra.weightage);
            });
        }
    }

    /**
   * @desc This function is being used to validate business Unit of user
   * <AUTHOR>
   * @since 12/10/2022
   * @param {string} businessUnit
   */
    businessUnit (businessUnit) {
        if (!businessUnit) {
            throw new GeneralError(this.__(REQUIRED, 'Business Unit'), 400);
        }
        if (!BUSINESS_UNIT.includes(businessUnit)) {
            throw new GeneralError(this.__(INVALID, 'Business Unit'), 400);
        }
    }
}

module.exports = Validator;
