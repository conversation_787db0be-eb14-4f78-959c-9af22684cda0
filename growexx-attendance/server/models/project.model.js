/**
 * @name project model
 * <AUTHOR>
*/
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const schema = new appMongoose.Schema({
    projectName: {
        type: String,
        unique: true,
        trim: true,
        min: 2,
        max: 100
    },
    users: [{
        empId: {
            type: appMongoose.Schema.Types.ObjectId,
            ref: 'user'
        },
        empRole: {
            type: String
        },
        startDate: {
            type: Date
        },
        endDate: {
            type: Date
        }
    }],
    pmUser: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        default: null
    },
    businessUnitId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'businessUnit',
        default: null
    },
    reviewManager: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'user',
        default: null
    },
    projectState: {
        type: String,
        enum: ['Active', 'Hold', 'Archived', 'Completed' ],
        default: 'Active'
    },
    portalId: {
        type: appMongoose.Schema.Types.ObjectId,
        ref: 'portal',
        default: null
    },
    jiraProjectId: {
        type: String
    },
    startDate: {
        type: Date
    },
    endDate: {
        type: Date
    },
    isActive: {
        type: Number,
        default: 1,
        // 0 = deactive, 1 = active
        enum: [0, 1]
    },
    isDelete: {
        type: Number,
        default: 0,
        enum: [0, 1]
    },
    createdAt: {
        type: Date,
        default: Date.now
    },
    updatedAt: {
        type: Date,
        default: Date.now
    }
});
schema.path('projectName').required(true, 'Project name cannot be blank');
schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);
module.exports = appMongoose.model('project', schema);
