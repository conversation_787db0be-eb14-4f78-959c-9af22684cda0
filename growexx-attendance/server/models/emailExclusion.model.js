/**
 * @name EmailExclusion model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');

const schema = new appMongoose.Schema({
    email: {
        type: String,
        required: true,
        unique: true,
        trim: true,
        lowercase: true,
        validate: {
            validator: function (v) {
                // Simple regex for basic email validation
                return /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(v);
            },
            message: props => `${props.value} is not a valid email address!`
        }
    },
    createdAt: {
        type: Date,
        default: Date.now
    }
});

module.exports = appMongoose.model('emailexclusion', schema);
