/**
 * @name Project Health Card Cache model
 * <AUTHOR>
 */
const appMongoose = require('mongoose');
const mongoosePaginate = require('mongoose-paginate-v2');
const aggregatePaginate = require('mongoose-aggregate-paginate-v2');

const epicEffortsInformationSchema = new appMongoose.Schema({
    storyCount: { type: Number, default: 0 },
    status: { type: String, trim: true },
    totalSalesEstimates: { type: String, default: '0.00', trim: true },
    totalLoggedEfforts: { type: String, default: '0.00', trim: true },
    toBeLoggedEfforts: { type: String, default: '0.00', trim: true },
    difference: { type: String, default: '0.00', trim: true },
    epicCount: { type: Number, default: 0 }
}, { _id: false });

const storiesWithExtremeSubtaskCountsSchema = new appMongoose.Schema({
    count: { type: Number, default: 0 },
    jiraStoryNos: { type: String, default: '', trim: true }
}, { _id: false });

const countInformationSchema = new appMongoose.Schema({
    totalSpent: { type: String, default: '0.00', trim: true },
    totalSalesEstimate: { type: String, default: '0.00', trim: true },
    totalDifference: { type: String, default: '0.00', trim: true },
    totalDevSpent: { type: String, default: '0.00', trim: true },
    totalDevToBeSpent: { type: String, default: '0.00', trim: true },
    totalEpicCount: { type: Number, default: 0 },
    totalStoryCount: { type: Number, default: 0 },
    storiesWithoutEpics: { type: Number, default: 0 },
    bugsWithoutLinkedStories: { type: Number, default: 0 },
    epicWithoutSE: { type: Number, default: 0 },
    storyWithOriginalEstimateGreaterThan40: { type: Number, default: 0 },
    storiesWithExtremeSubtaskCounts: { type: storiesWithExtremeSubtaskCountsSchema, default: () => ({}) },
    subtasksCount: { type: Number, default: 0 },
    jiraStoryUrl: { type: String, default: '', trim: true }
}, { _id: false });

const schema = new appMongoose.Schema({
    projectId: { type: String, required: true, index: true },
    boardId: { type: String, required: true, index: true },
    epicEffortsInformation: [epicEffortsInformationSchema],
    countInformation: countInformationSchema,
    createdAt: { type: Date, default: Date.now }
});

schema.plugin(mongoosePaginate);
schema.plugin(aggregatePaginate);

module.exports = appMongoose.model('ProjectHealthCard', schema);
