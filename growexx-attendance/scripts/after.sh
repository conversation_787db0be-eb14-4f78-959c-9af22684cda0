#!/bin/bash
export  PATH="/home/<USER>/.nvm/versions/node/v14.16.0/bin/:$PATH"
cd /home/<USER>/api
if [ "$DEPLOYMENT_GROUP_NAME" == "group-dev-growexx-attendance-api" ]; then
    ###Copying env file####
    #aws s3 cp s3://growexx-dev-attendance-ci/config/development.env /home/<USER>/api
    NODE_ENV=development node getEnvs.js timesheet
    NODE_ENV=development node_modules/migrate-mongo/bin/migrate-mongo.js status
    NODE_ENV=development node_modules/migrate-mongo/bin/migrate-mongo.js up
    NODE_ENV=development node_modules/migrate-mongo/bin/migrate-mongo.js status
    pm2 start development.json

elif [ "$DEMO_GROUP_NAME" == "group-demo-growexx-attendance-api" ]; then
    ###Copying env file####
    #aws s3 cp s3://growexx-dev-attendance-ci/config/demo.env /home/<USER>/api
    NODE_ENV=demo node getEnvs.js timesheet
    NODE_ENV=demo node_modules/migrate-mongo/bin/migrate-mongo.js status
    NODE_ENV=demo node_modules/migrate-mongo/bin/migrate-mongo.js up
    NODE_ENV=demo node_modules/migrate-mongo/bin/migrate-mongo.js status
    pm2 start demo.json

elif [ "$DEPLOYMENT_GROUP_NAME" == "group-prod-growexx-attendance-api" ]; then
   #aws s3 cp s3://growexx-prod-attendance-ci/config/production.env /home/<USER>/api
   ###Copying env file####
   cd /home/<USER>/api
   NODE_ENV=production node getEnvs.js timesheet
   NODE_ENV=production node_modules/migrate-mongo/bin/migrate-mongo.js status
   NODE_ENV=production node_modules/migrate-mongo/bin/migrate-mongo.js up
   NODE_ENV=production node_modules/migrate-mongo/bin/migrate-mongo.js status

   pm2 start production.json
   
else
  echo "Deployment failed"
fi
