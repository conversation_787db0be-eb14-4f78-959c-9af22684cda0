import React from 'react';
import PropTypes from 'prop-types'; // Import PropTypes for props validation
import { Modal, Form, Input, notification } from 'antd';
import request from 'utils/request';
import { FormattedMessage } from 'react-intl';
import { API_ENDPOINTS } from '../constants';
import messages from './messages';
import { TEST_IDS } from './stub/test.stub';

const AddDesignation = ({ stateValues, toggleModal }) => {
  const [form] = Form.useForm();

  const handleFormSubmit = async values => {
    try {
      const payload = {
        method: 'POST',
        body: values,
      };
      request(API_ENDPOINTS.ADD_DESIGNATION, payload)
        .then(res => {
          notification.success({
            message: <FormattedMessage {...messages.newDesignationAdded} />,
            description: res.message,
          });
        })
        .catch(error => {
          // Try to get a meaningful error message
          const errorMsg =
            error?.message ||
            error?.response?.data?.message ||
            error?.response?.statusText ||
            'Failed to add designation.';

          // Check for 409 Conflict or "already exist" in the message
          if (
            (error?.response && error.response.status === 409) ||
            errorMsg.toLowerCase().includes('already exist')
          ) {
            notification.warning({
              message: 'Designation already exists',
              description:
                'The designation you are trying to add already exists.',
            });
          } else {
            notification.error({
              message: <FormattedMessage {...messages.failedToLoad} />,
              description: errorMsg,
            });
          }
        });
      form.resetFields();
      toggleModal();
    } catch (error) {
      // Use a custom logger or comment out for production
      if (process.env.NODE_ENV === 'development') {
        // console.error('Failed to save designation:', error);
      }
    }
  };

  const layout = {
    labelCol: {
      span: 7,
      xs: 24,
      sm: 7,
    },
    wrapperCol: {
      span: 14,
      xs: 24,
      sm: 14,
    },
  };

  return (
    <>
      <Modal
        title="Add Designation"
        visible={stateValues.showDesignationFormModal}
        onOk={() => form.submit()} // Trigger form submission programmatically
        onCancel={() => toggleModal()}
        okButtonProps={{
          'data-testid': TEST_IDS.ADD_USER_SUBMIT_BUTTON,
        }}
        okText={<FormattedMessage {...messages.addOKLabel} />}
        cancelButtonProps={{
          'data-testid': TEST_IDS.ADD_USER_CANCEL_BUTTON,
        }}
      >
        <Form
          form={form}
          {...layout}
          onFinish={handleFormSubmit} // Handles form submission when submitted
        >
          <Form.Item
            name="name"
            label="Add Designation"
            rules={[
              { required: true, message: 'Please enter the designation' },
            ]}
          >
            <Input placeholder="Enter designation" />
          </Form.Item>
        </Form>
      </Modal>
    </>
  );
};

// Add PropTypes validation
AddDesignation.propTypes = {
  stateValues: PropTypes.shape({
    showDesignationFormModal: PropTypes.bool.isRequired,
  }).isRequired,
  toggleModal: PropTypes.func.isRequired,
};

export default AddDesignation;
