import React, { PureComponent } from 'react';
import { with<PERSON>outer } from 'react-router-dom';
import { connect } from 'react-redux';
import { compose } from 'redux';
import { Field, reduxForm } from 'redux-form';
import { createStructuredSelector } from 'reselect';
import { Modal, Form, notification } from 'antd';
import { FormattedMessage } from 'react-intl';
import moment from 'moment';
import PropTypes from 'prop-types';
import { ARangePicker, ASelect } from 'utils/Fields';
import request from 'utils/request';
import useInjectReducer from 'utils/injectReducer';
import makeSelectConnectedJiraList from './selectors';
import reducer from './reducer';
import * as actions from './actions';
import { TEST_IDS } from './stub/test.stub';
import messages from './messages';
import {
  LEAVE_TYPES,
  USER_LIST_FORM_KEY,
  ALL_LEAVE_DURATION,
  SECOND_HALF_LEAVE_DURATION,
  FIRST_HALF_LEAVE_DURATION,
} from './constants';
import {
  API_ENDPOINTS,
  GENERIC_MOMENT_DATE_FORMAT,
  WEEKEND,
} from '../constants';

class AddLeave extends PureComponent {
  /**
   * updateLeaveHandler
   */
  updateLeaveHandler = async () => {
    const {
      userStoreData,
      reset,
      stateValues: { showAddLeaveModal, userId },
      updateState,
    } = this.props;

    updateState({
      isLoading: true,
    });
    const body = {
      leaveType: userStoreData.leaveType,
      startDate:
        userStoreData.leaveRange.length > 0 &&
        moment(userStoreData.leaveRange[0]).format(GENERIC_MOMENT_DATE_FORMAT),
      endDate:
        userStoreData.leaveRange.length > 0 &&
        moment(userStoreData.leaveRange[1]).format(GENERIC_MOMENT_DATE_FORMAT),
      startDateDuration: userStoreData.fromLeaveDuration,
      endDateDuration: userStoreData.toLeaveDuration,
      employeeId: userId.employeeId,
    };
    const payload = {
      method: 'POST',
      body,
    };
    const URL = API_ENDPOINTS.ADD_USERS_LEAVE;

    await request(URL, payload)
      .then(res => {
        updateState({
          isLoading: false,
          showAddLeaveModal: !showAddLeaveModal,
          userId: '',
        });
        notification.success({
          description: res.message,
        });
        reset();
      })
      .catch(error => {
        if (error.response && error.response.status === 400) {
          notification.error({
            description: 'Action Cannot Be Performed',
          });
        } else {
          error.response.json().then(err =>
            notification.error({
              description: err.message,
            }),
          );
        }
        updateState({
          isLoading: false,
        });
      });
  };

  /**
   * getValueForSameDate - for same dates
   * @returns dropdown values
   */
  getValueForSameDate = () => {
    const {
      userStoreData: { fromLeaveDuration },
      dispatchLeaveChanges,
    } = this.props;
    const returnValue = {
      from: ALL_LEAVE_DURATION,
      to: ALL_LEAVE_DURATION,
    };
    // eslint-disable-next-line no-plusplus
    for (let index = 0; index < ALL_LEAVE_DURATION.length; index++) {
      if (fromLeaveDuration === ALL_LEAVE_DURATION[index].value) {
        returnValue.to = [ALL_LEAVE_DURATION[index]];
        dispatchLeaveChanges(
          'toLeaveDuration',
          ALL_LEAVE_DURATION[index].value,
        );
        return returnValue;
      }
    }
    return returnValue;
  };

  /**
   * getValueForDifferentDate - for different dates
   * @returns dropdown values
   */
  getValueForDifferentDate = () => {
    const {
      userStoreData: { toLeaveDuration },
      dispatchLeaveChanges,
    } = this.props;
    if (toLeaveDuration === ALL_LEAVE_DURATION[2].value) {
      dispatchLeaveChanges('toLeaveDuration', ALL_LEAVE_DURATION[0].value);
    }
    return {
      from: SECOND_HALF_LEAVE_DURATION,
      to: FIRST_HALF_LEAVE_DURATION,
    };
  };

  /**
   * getLeaveDuration
   * @returns Leave Duration
   */
  getLeaveDuration = () => {
    const defaultValues = {
      from: ALL_LEAVE_DURATION,
      to: ALL_LEAVE_DURATION,
    };
    const {
      userStoreData: { leaveRange },
    } = this.props;
    if (leaveRange && moment(leaveRange[0]).isSame(moment(leaveRange[1]))) {
      return this.getValueForSameDate();
    }
    if (leaveRange && !moment(leaveRange[0]).isSame(moment(leaveRange[1]))) {
      return this.getValueForDifferentDate();
    }
    return defaultValues;
  };

  /**
   * toggleAddLeaveModal
   */
  toggleAddLeaveModal = () => {
    const {
      stateValues: { showAddLeaveModal },
      updateState,
    } = this.props;
    updateState({
      showAddLeaveModal: !showAddLeaveModal,
      userId: '',
    });
  };

  /**
   * getDisabledDates
   * @param {object} current
   * @returns dates to be disabled
   */
  getDisabledDates = current =>
    current && WEEKEND.some(day => day === current.day());

  render() {
    const {
      pristine,
      submitting,
      invalid,
      updateField,
      userStoreData: { leaveRange },
      stateValues: { showAddLeaveModal, userId, isLoading },
    } = this.props;
    const performingAction = pristine || submitting || invalid || isLoading;
    const cancelDisabled = submitting || isLoading;
    const leaveRangeAdded =
      leaveRange !== null && leaveRange !== undefined && leaveRange.length > 1;
    const disabledCondition = performingAction || !leaveRangeAdded;
    return (
      <Modal
        title={
          <FormattedMessage
            {...messages.addUserLeaveTitle}
            values={{
              name: `${userId.firstName} ${userId.lastName}`,
            }}
          />
        }
        visible={showAddLeaveModal}
        onOk={this.updateLeaveHandler}
        confirmLoading={isLoading}
        onCancel={() => this.toggleAddLeaveModal()}
        okButtonProps={{
          disabled: disabledCondition,
          'data-testid': TEST_IDS.ADD_LEAVE_SUBMIT_BUTTON,
        }}
        okText={<FormattedMessage {...messages.addOKLabel} />}
        cancelButtonProps={{
          disabled: cancelDisabled,
          'data-testid': TEST_IDS.ADD_LEAVE_CANCEL_BUTTON,
        }}
      >
        <Form onSubmit={this.updateLeaveHandler}>
          <Field
            label="Leave Type"
            name="leaveType"
            component={ASelect}
            placeholder="Leave Type"
            options={LEAVE_TYPES}
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('leaveType', e)}
            required
            hasFeedback
          />
          <Field
            label="Date Range"
            name="leaveRange"
            component={ARangePicker}
            disabledDate={this.getDisabledDates}
            placeholder={['From', 'To']}
            hasFeedback
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('leaveRange', e)}
            required
            allowClear
          />
          <Field
            label="Start Duration"
            name="fromLeaveDuration"
            component={ASelect}
            placeholder="From Leave Duration"
            options={this.getLeaveDuration().from}
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('fromLeaveDuration', e)}
            required
            hasFeedback
          />
          <Field
            label="End Duration"
            name="toLeaveDuration"
            component={ASelect}
            placeholder="From Leave Duration"
            options={this.getLeaveDuration().to}
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('toLeaveDuration', e)}
            required
            hasFeedback
          />
        </Form>
      </Modal>
    );
  }
}
const mapStateToProps = state => {
  const getStateValues = createStructuredSelector({
    userStoreData: makeSelectConnectedJiraList(),
  });
  return {
    formState: state,
    ...getStateValues(state),
  };
};

export const mapDispatchToProps = dispatch => ({
  updateField: (key, value) => dispatch(actions.updateField(key, value)),
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

AddLeave.propTypes = {
  // props from parent
  stateValues: PropTypes.object,
  updateState: PropTypes.func,
  dispatchLeaveChanges: PropTypes.func,

  // Redux-form
  dispatch: PropTypes.func.isRequired,
  invalid: PropTypes.bool,
  formState: PropTypes.object,
  reset: PropTypes.func.isRequired,
  pristine: PropTypes.bool,
  submitting: PropTypes.bool,
  // Action
  updateField: PropTypes.func.isRequired,
  // Store
  userStoreData: PropTypes.object,
};

const withReducer = useInjectReducer({
  key: USER_LIST_FORM_KEY,
  reducer,
});

export default compose(
  withRouter,
  withReducer,
  withConnect,
  reduxForm({
    form: USER_LIST_FORM_KEY,
    fields: ['leaveType', 'leaveRange', 'fromLeaveDuration', 'toLeaveDuration'],
  }),
)(AddLeave);
