import React from 'react';
import { render } from '@testing-library/react';
import { MemoryRouter } from 'react-router-dom';
import { getUserData } from 'utils/Helper';
import request from 'utils/request';
import ReviewLogs from '../index';

jest.mock('utils/request', () => jest.fn());

jest.mock('utils/Helper', () => ({
  getUserData: jest.fn(),
  userExists: jest.fn(() => true),
}));

describe('ReviewLogs approve/reject text button visibility', () => {
  const baseLog = (_id, userId) => ({
    _id,
    userId,
    logStatus: 0,
    jiraProjectName: 'ProjectX',
    ticket: 'AL-279',
    jiraTitle: 'Test Log',
    timeSpentHours: 8,
  });

  const getLogsList = userIds => [
    {
      id: 'member1',
      name: 'User 1',
      isAnyPendingForWeek: true,
      weeklyLogs: [
        {
          date: '2024-07-01',
          isAnyPendingForDay: true,
          logs: userIds.map((uid, idx) => baseLog(`log${idx}`, uid)),
        },
      ],
    },
  ];

  // eslint-disable-next-line no-unused-vars
  const defaultState = {
    name: 'User 1',
    showNotification: false,
    notificationMessage: '',
    startDate: '2024-07-01',
    endDate: '2024-07-07',
    currentDate: '2024-07-01',
    isLoading: false,
    projectList: [{ value: 'p1', label: 'Project 1' }],
    projectId: 'p1',
    logsList: [],
    anyUpdating: false,
    reviewDatePassed: false,
    isAnyPendingForTeam: true,
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('does NOT render week/day approve/reject text buttons if any log belongs to logged-in user', async () => {
    getUserData.mockReturnValue({ _id: 'user123' });
    const logsList = getLogsList(['user123', 'otherUser']);
    request.mockResolvedValue({ data: logsList });

    const { queryByText } = render(
      <MemoryRouter>
        <ReviewLogs />
      </MemoryRouter>,
    );

    // Wait for the component to finish loading (if it shows a loading indicator)
    // await findByText('Some text that appears after loading');

    // Approve/Reject text buttons should not be present
    expect(queryByText('Approve')).toBeNull();
    expect(queryByText('Reject')).toBeNull();
  });

  it('renders week/day approve/reject text buttons if no log belongs to logged-in user', async () => {
    getUserData.mockReturnValue({ _id: 'user123' });
    const logsList = getLogsList(['otherUser', 'anotherUser']);
    request.mockResolvedValue({ data: logsList });

    const { findByText } = render(
      <MemoryRouter>
        <ReviewLogs />
      </MemoryRouter>,
    );

    // Wait for the buttons to appear
    expect(await findByText('Approve All')).not.toBeNull();
    expect(await findByText('Reject All')).not.toBeNull();
  });
});
