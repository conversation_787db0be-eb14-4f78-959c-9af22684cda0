/**
 * Testing the EpicNameMultiSelectFilter Component
 */

import React from 'react';
import { render, waitFor, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import { ConnectedRouter } from 'connected-react-router';
import history from 'utils/history';
import request from 'utils/request';
import { browserHistory } from 'react-router-dom';
import configureStore from '../../../configureStore';
import EpicNameMultiSelectFilter from '../EpicNameMultiSelectFilter';

let store;

jest.mock('utils/request');

const mockEpicNames = [
  { value: 'Epic 1', text: 'Epic 1' },
  { value: 'Epic 2', text: 'Epic 2' },
  { value: 'Epic 3', text: 'Epic 3' },
  { value: 'July Tasks Dhruv', text: 'July Tasks Dhruv' },
  { value: 'July Tasks Dinesh', text: 'July Tasks Dinesh' },
];

const mockParentFilterInstance = jest.fn();

const componentWrapper = (props = {}) =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <EpicNameMultiSelectFilter
            parentFilterInstance={mockParentFilterInstance}
            {...props}
          />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<EpicNameMultiSelectFilter />', () => {
  beforeEach(() => {
    store = configureStore({}, browserHistory);
    delete window.location;
    window.location = { search: '?boardId=123' };
    mockParentFilterInstance.mockClear();
    request.mockClear();

    request.mockImplementation(url => {
      if (url.includes('/epic-names')) {
        return Promise.resolve({
          status: 1,
          data: mockEpicNames,
        });
      }
      return Promise.resolve({});
    });
  });

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('Should render the multi-select filter', () => {
    const { container } = componentWrapper();
    expect(container.querySelector('.ant-select')).toBeTruthy();
  });

  it('Should fetch epic names when dropdown is opened', async () => {
    const { getByRole } = componentWrapper();

    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));

    await waitFor(() => {
      expect(request).toHaveBeenCalled();
      expect(request.mock.calls[0][0]).toContain('/epic-names');
      expect(request.mock.calls[0][1]).toEqual({ method: 'GET' });
    });
  });

  it('Should allow multiple epic selection without immediate API calls', async () => {
    // Create a mock implementation that doesn't call the callback immediately
    const mockInstance = {
      onFloatingFilterChanged: jest.fn(),
      getFilterValue: () => [],
    };
    mockParentFilterInstance.mockImplementation(() => mockInstance);

    const { getByRole, findAllByRole } = componentWrapper();

    // Open dropdown
    fireEvent.mouseDown(getByRole('combobox'));

    // Wait for options to load
    const options = await findAllByRole('option');
    expect(options.length).toBeGreaterThan(0);

    // Select multiple options
    fireEvent.click(options[0]); // Select first option
    fireEvent.click(options[1]); // Select second option

    // Verify the callback wasn't called through the parent instance
    expect(mockParentFilterInstance).toHaveBeenCalled();
    expect(mockInstance.onFloatingFilterChanged).not.toHaveBeenCalled();
  });

  it('Should apply filter when Apply button is clicked', async () => {
    // Create a mock implementation that tracks calls
    const mockOnFloatingFilterChanged = jest.fn();
    const mockInstance = {
      onFloatingFilterChanged: mockOnFloatingFilterChanged,
      getFilterValue: () => [],
    };

    const mockParentInstance = jest.fn(callback => {
      callback(mockInstance);
      return mockInstance;
    });

    const { getByRole, getByText } = componentWrapper({
      parentFilterInstance: mockParentInstance,
    });

    // Clear any previous calls
    mockOnFloatingFilterChanged.mockClear();
    mockParentInstance.mockClear();

    // Open dropdown
    const select = getByRole('combobox');
    fireEvent.mouseDown(select);

    // Wait for options to be loaded
    await waitFor(() => {
      const options = document.querySelectorAll('.ant-select-item-option');
      expect(options.length).toBeGreaterThan(0);
    });

    // Select an option
    const options = document.querySelectorAll('.ant-select-item-option');
    const selectedEpic = options[0].textContent;
    fireEvent.click(options[0]);

    // Click Apply button
    const applyButton = getByText('Apply');
    fireEvent.click(applyButton);

    // The filter should now be applied
    await waitFor(() => {
      expect(mockOnFloatingFilterChanged).toHaveBeenCalledTimes(1);
      const filterValue = mockOnFloatingFilterChanged.mock.calls[0][1];
      expect(filterValue).toContain(selectedEpic);
    });
  });

  it('Should apply filter when Enter is pressed in search input', async () => {
    // Create a mock implementation that tracks calls
    const mockOnFloatingFilterChanged = jest.fn();
    const mockInstance = {
      onFloatingFilterChanged: mockOnFloatingFilterChanged,
      getFilterValue: () => [],
    };

    const mockParentInstance = jest.fn(callback => {
      callback(mockInstance);
      return mockInstance;
    });

    const { getByRole, getByPlaceholderText, getByText } = componentWrapper({
      parentFilterInstance: mockParentInstance,
    });

    // Clear any previous calls
    mockOnFloatingFilterChanged.mockClear();
    mockParentInstance.mockClear();

    // Open dropdown
    const select = getByRole('combobox');
    fireEvent.mouseDown(select);

    // Wait for options to be loaded
    await waitFor(() => {
      const options = document.querySelectorAll('.ant-select-item-option');
      expect(options.length).toBeGreaterThan(0);
    });

    // Select an option
    const options = document.querySelectorAll('.ant-select-item-option');
    const selectedEpic = options[0].textContent;
    fireEvent.click(options[0]);

    // Find the search input and type something
    const searchInput = getByPlaceholderText('Search epic names...');
    fireEvent.change(searchInput, { target: { value: 'test' } });

    // Press Enter in the search input
    fireEvent.keyDown(searchInput, {
      key: 'Enter',
      code: 'Enter',
      keyCode: 13,
      which: 13,
    });

    // Click Apply button to apply the filter
    const applyButton = getByText('Apply');
    fireEvent.click(applyButton);

    // The filter should now be applied
    await waitFor(() => {
      expect(mockOnFloatingFilterChanged).toHaveBeenCalledTimes(1);
      const filterValue = mockOnFloatingFilterChanged.mock.calls[0][1];
      expect(filterValue).toContain(selectedEpic);
    });
  });

  it('Should clear filter when all selections are removed and Apply is clicked', async () => {
    // Create a mock implementation that tracks calls
    const mockOnFloatingFilterChanged = jest.fn();
    const mockParentInstance = jest.fn(callback => {
      callback({
        onFloatingFilterChanged: mockOnFloatingFilterChanged,
      });
    });

    // Render the component with the mock parent instance
    const { getByRole, getByText, container } = render(
      <Provider store={store}>
        <IntlProvider locale="en">
          <ConnectedRouter history={history}>
            <EpicNameMultiSelectFilter
              parentFilterInstance={mockParentInstance}
              column={{ id: 'epicName' }}
            />
          </ConnectedRouter>
        </IntlProvider>
      </Provider>,
    );

    // Get the select input
    const select = getByRole('combobox');

    // Open dropdown
    fireEvent.mouseDown(select);
    await waitFor(() => {
      expect(
        document.querySelector('.ant-select-dropdown'),
      ).toBeInTheDocument();
    });

    // Wait for options to be loaded
    await waitFor(() => {
      const options = document.querySelectorAll('.ant-select-item-option');
      expect(options.length).toBeGreaterThan(0);
    });

    // Select an option
    const options = document.querySelectorAll('.ant-select-item-option');
    fireEvent.click(options[0]);

    // Click Apply button to apply the filter
    const applyButton = getByText('Apply');
    fireEvent.click(applyButton);

    // Wait for the selection to be applied
    await waitFor(() => {
      expect(mockOnFloatingFilterChanged).toHaveBeenCalled();
    });

    // Reset mocks for the next part of the test
    mockOnFloatingFilterChanged.mockClear();
    mockParentInstance.mockClear();

    // Open dropdown again
    fireEvent.mouseDown(select);
    await waitFor(() => {
      expect(
        document.querySelector('.ant-select-dropdown'),
      ).toBeInTheDocument();
    });

    // Find and click the clear icon to remove all selections
    const clearIcon = container.querySelector('.ant-select-clear');
    if (clearIcon) {
      fireEvent.mouseDown(clearIcon);

      // Click Apply button to apply the empty filter
      const applyButton2 = getByText('Apply');
      fireEvent.click(applyButton2);

      // The filter should be cleared
      await waitFor(() => {
        expect(mockParentInstance).toHaveBeenCalledTimes(1);

        // Get the callback that was passed to the parent instance
        const [callback] = mockParentInstance.mock.calls[0];
        const instance = { onFloatingFilterChanged: jest.fn() };

        // Call the callback with our mock instance
        callback(instance);

        // Verify that onFloatingFilterChanged was called with the expected arguments
        expect(instance.onFloatingFilterChanged).toHaveBeenCalledWith(
          null, // First argument is null when clearing
          null, // Second argument is null when clearing
        );
      });
    }
  });

  it.skip('Should show correct placeholder text based on selection state', async () => {
    // Create a mock implementation
    const mockOnFloatingFilterChanged = jest.fn();
    const mockParentInstance = jest.fn(callback => {
      callback({
        onFloatingFilterChanged: mockOnFloatingFilterChanged,
      });
    });

    const { getByRole, getByText } = componentWrapper({
      parentFilterInstance: mockParentInstance,
    });

    // Initial placeholder should be 'Select epic names...'
    const select = getByRole('combobox');
    // Check that the placeholder text is in the document
    expect(
      document.querySelector('.ant-select-selection-placeholder')?.textContent,
    ).toBe('Select epic names...');

    // Open dropdown and select an option
    fireEvent.mouseDown(select);
    await waitFor(() => {
      const options = document.querySelectorAll('.ant-select-item-option');
      expect(options.length).toBeGreaterThan(0);
      fireEvent.click(options[0]);
    });

    // After selection, placeholder should indicate selection but not applied yet
    expect(
      document.querySelector('.ant-select-selection-placeholder')?.textContent,
    ).toBe('1 epic(s) selected - close dropdown to apply filter');

    // Click Apply
    const applyButton = getByText('Apply');
    fireEvent.click(applyButton);

    // After applying, the placeholder should show the selection is applied
    // We need to wait for the state to update
    await waitFor(() => {
      expect(
        // eslint-disable-next-line prettier/prettier
        document.querySelector(
          '.ant-select-selection-placeholder',
          // eslint-disable-next-line prettier/prettier}
          // eslint-disable-next-line prettier/prettier
        )?.textContent,
      ).toBe('1 epic(s) selected - close dropdown to apply filter');
    });
  });

  it('Should properly handle epic names with spaces when applying filter', async () => {
    // Create a mock implementation with unique variable names
    const mockSpaceFilterChanged = jest.fn();
    const mockSpaceInstance = {
      onFloatingFilterChanged: mockSpaceFilterChanged,
      getFilterValue: () => [],
    };

    const mockSpaceParentInstance = jest.fn(callback => {
      callback(mockSpaceInstance);
      return mockSpaceInstance;
    });

    // Mock the request function to return our test data
    request.mockImplementationOnce(() =>
      Promise.resolve({
        status: 1,
        data: [
          { value: 'July Tasks Dhruv', text: 'July Tasks Dhruv' },
          { value: 'July Tasks Dinesh', text: 'July Tasks Dinesh' },
        ],
      }),
    );

    const { getByRole, getByText } = componentWrapper({
      parentFilterInstance: mockSpaceParentInstance,
    });

    // Clear any previous calls to the mock
    mockSpaceParentInstance.mockClear();
    mockSpaceFilterChanged.mockClear();

    // Open dropdown
    const select = getByRole('combobox');
    fireEvent.mouseDown(select);

    // Wait for options to be loaded
    await waitFor(() => {
      const options = document.querySelectorAll('.ant-select-item-option');
      expect(options.length).toBeGreaterThan(0);
    });

    // Find and select an epic with spaces in the name
    const options = Array.from(
      document.querySelectorAll('.ant-select-item-option'),
    );

    // Find the exact epic we're looking for
    const epicWithSpace = Array.from(options).find(
      opt => opt.textContent && opt.textContent.trim() === 'July Tasks Dhruv',
    );

    // Make sure we found an epic with spaces
    expect(epicWithSpace).toBeDefined();
    fireEvent.click(epicWithSpace);

    // Click Apply
    const applyButton = getByText('Apply');
    fireEvent.click(applyButton);

    // Verify the filter was applied with the correct epic name
    await waitFor(() => {
      expect(mockSpaceFilterChanged).toHaveBeenCalledTimes(1);

      // Get the filter value passed to onFloatingFilterChanged
      const filterValue = mockSpaceFilterChanged.mock.calls[0][1];

      // The filter value should be a string containing the selected epic name
      expect(typeof filterValue).toBe('string');
      expect(filterValue).toContain('July Tasks Dhruv');
    });
  });
});
