/* eslint-disable no-unused-vars */
import React, { useState, useEffect, useCallback, useRef } from 'react';
import PropTypes from 'prop-types';
import { Select, Tooltip, Tag } from 'antd';
import { debounce } from 'lodash';
import request from 'utils/request';
import { API_ENDPOINTS } from 'containers/constants';
import styled from 'styled-components';

// Styled components for better tag rendering
const StyledTag = styled(Tag)`
  display: inline-flex;
  align-items: center;

  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  margin: 2px 8px 2px 0; /* more right margin for tags */
  line-height: 20px;
  padding: 0 8px;
  height: 24px;
  font-size: 13px;
  border-radius: 4px;
  background: #f0f7ff !important;
  border: 1px solid #d9e7ff !important;
  color: #1677ff;

  .ant-tag-close-icon {
    color: #1677ff;
    margin-left: 4px;
    font-size: 12px;
    opacity: 0.75;

    &:hover {
      opacity: 1;
    }
  }
`;

const SelectWrapper = styled.div`
  .ant-select {
    width: 100%;
    min-width: 250px;
    position: relative;

    .ant-select-selector {
      cursor: default !important;
      caret-color: transparent !important;
      padding-right: 8px !important;
    }

    .ant-select-selection-search {
      cursor: default !important;
      input {
        cursor: default !important;
        caret-color: transparent !important;
      }
    }

    .ant-select-arrow {
      right: 8px;
      color: rgba(0, 0, 0, 0.25);
      font-size: 12px;
      pointer-events: none;
    }

    &:hover .ant-select-selector {
      border-color: #4096ff !important;
    }

    &.ant-select-focused .ant-select-selector {
      border-color: #4096ff !important;
      box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
    }
  }

  .ant-select-selector {
    width: 210px !important;
    // min-width: 280px !important;
    // max-width: 280px !important;
    flex: none !important;
    min-height: 40px !important;
    height: auto !important;
    min-height: 40px !important;
    line-height: 36px !important;
    padding: 2px 16px 2px 8px !important;
    overflow-x: auto !important;
    overflow-y: hidden !important;
    border-radius: 6px !important;
    border: 1px solid #d9d9d9 !important;
    transition: all 0.3s;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    display: flex;
    align-items: center;
    cursor: pointer;

    &::-webkit-scrollbar {
      display: none; /* Webkit browsers */
    }
  }

  .ant-select-selection-overflow {
    min-width: 0 !important;
    width: 100% !important;
    max-width: 100% !important;
    flex: 1 1 0% !important;
    display: flex;
    flex-wrap: nowrap;
    gap: 6px;
    min-height: 36px;
    align-items: center;
    overflow-x: auto;
    overflow-y: hidden;
    white-space: nowrap;
    scrollbar-width: none; /* Firefox */
    -ms-overflow-style: none; /* IE 10+ */
    pointer-events: auto;
  }

  .ant-select-selection-overflow::-webkit-scrollbar {
    display: none; /* Webkit browsers */
  }

  .ant-select-selection-overflow::after {
    content: '';
    display: inline-block;
    width: 2px;
    flex-shrink: 0;
  }
  .ant-select-selection-item {
    margin: 0 !important;
    padding: 0 !important;
    background: transparent !important;
    border: none !important;
    height: auto !important;
    line-height: 1.2;
  }

  .ant-select-selection-search {
    margin-left: 0;
    padding: 0 2px;
  }

  .ant-select-selection-placeholder {
    padding-left: 4px;
    color: #8c8c8c;
  }
`;

const { Option } = Select;

const EpicNameMultiSelectFilter = React.forwardRef(props => {
  const [currentValue, setCurrentValue] = useState([]);
  const [searchValue, setSearchValue] = useState('');
  const [epicOptions, setEpicOptions] = useState([]);
  const [loading, setLoading] = useState(false);
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);
  const [isApplyingFilter, setIsApplyingFilter] = useState(false);
  const [initialized, setInitialized] = useState(false);
  const [selectedValues, setSelectedValues] = useState([]);
  const [hasChanges, setHasChanges] = useState(false);
  const searchInputRef = useRef(null);
  // Ref for tag overflow scroll
  const tagOverflowRef = useRef(null);

  // Get boardId from the URL or props
  const getBoardId = () => {
    const query = new URLSearchParams(window.location.search);
    return query.get('boardId');
  };

  // Fetch epic names from API
  const fetchEpicNames = useCallback(async (search = '') => {
    const boardId = getBoardId();
    if (!boardId) return;

    setLoading(true);
    try {
      const url = `${
        API_ENDPOINTS.GET_EPIC_NAMES
      }?boardId=${boardId}&search=${encodeURIComponent(search)}`;
      const response = await request(url, { method: 'GET' });

      if (response && response.data) {
        setEpicOptions(response.data);
      }
    } catch (error) {
      // Error fetching epic names
      setEpicOptions([]);
    } finally {
      setLoading(false);
    }
  }, []);

  // Debounced search function
  const debouncedSearch = useCallback(
    debounce(searchTerm => {
      fetchEpicNames(searchTerm);
    }, 500),
    [fetchEpicNames],
  );

  // Initial load: only handle URL parameters, do NOT fetch epic names
  useEffect(() => {
    const initializeFilter = async () => {
      // Check for initial values from URL parameters
      if (!initialized) {
        const query = new URLSearchParams(window.location.search);
        const epicNameParam = query.get('epicName');

        if (epicNameParam) {
          // Handle multiple epic names separated by & (URL decoded automatically by URLSearchParams)
          let initialValues = [];
          if (epicNameParam.includes('&')) {
            initialValues = epicNameParam
              .split('&')
              .map(name => name.trim())
              .filter(name => name);
          } else if (epicNameParam.includes(',')) {
            // Backward compatibility for comma-separated values
            initialValues = epicNameParam
              .split(',')
              .map(name => name.trim())
              .filter(name => name);
          } else {
            initialValues = [epicNameParam.trim()];
          }

          if (initialValues.length > 0) {
            setCurrentValue(initialValues);
            // Apply the filter immediately for URL parameters
            applyFilter(initialValues);
          }
        }
        setInitialized(true);
      }
    };

    initializeFilter();
  }, []);

  // Cleanup debounced functions on unmount
  useEffect(
    () => () => {
      debouncedSearch.cancel();
    },
    [debouncedSearch],
  );

  // Handle search input change
  const handleSearch = value => {
    setSearchValue(value);
    debouncedSearch(value);
  };

  // Handle key down events
  const handleKeyDown = e => {
    if (e.key === 'Enter' && isDropdownOpen) {
      // Prevent default behavior when dropdown is open
      e.preventDefault();
      e.stopPropagation();
      return false;
    }
    return undefined;
  };

  // Apply the filter to the grid
  const applyFilter = useCallback(
    values => {
      setIsApplyingFilter(true);
      setCurrentValue([...values]);

      if (!values || values.length === 0) {
        // Clear filter
        props.parentFilterInstance(instance => {
          instance.onFloatingFilterChanged(null, null);
        });
      } else {
        // Apply filter with multiple epic names joined by &
        const filterValue = values.join('&');
        props.parentFilterInstance(instance => {
          instance.onFloatingFilterChanged('', filterValue);
        });
      }

      // Close dropdown and reset states
      setIsDropdownOpen(false);
      setSearchValue('');

      // Reset applying state after a short delay
      setTimeout(() => {
        setIsApplyingFilter(false);
      }, 500);
    },
    [props],
  );

  // Handle Apply button click
  const handleApply = e => {
    // Prevent blur event from interfering
    if (e) {
      e.stopPropagation();
    }
    // Ensure selectedValues is an array
    const valuesToApply = Array.isArray(selectedValues) ? selectedValues : [];
    applyFilter(valuesToApply);
    setCurrentValue([...valuesToApply]);
    setHasChanges(false);
    setIsDropdownOpen(false);
  };

  // Handle Cancel button click
  const handleCancel = () => {
    setSelectedValues([...currentValue]);
    setIsDropdownOpen(false);
  };

  // Handle filter selection - store in local state until Apply is clicked
  const onCustomFilterSelect = value => {
    setSelectedValues(value);
    setHasChanges(true);
  };

  // Sync currentValue and selectedValues with parent filter
  useEffect(() => {
    if (props.parentFilterInstance) {
      try {
        const filterValue = props.parentFilterInstance().getFilterValue() || [];
        const safeFilterValue = Array.isArray(filterValue) ? filterValue : [];
        setCurrentValue(safeFilterValue);
        setSelectedValues(safeFilterValue);
      } catch (error) {
        // Log to error reporting service in production
        if (process.env.NODE_ENV !== 'production') {
          // eslint-disable-next-line no-console
          console.error('Error syncing filter values:', error);
        }
      }
    }
  }, [props.parentFilterInstance]);

  // Handle dropdown open/close
  const handleDropdownVisibleChange = open => {
    if (open) {
      // Always refresh the values from the parent when opening
      try {
        const currentFilterValue = props.parentFilterInstance
          ? props.parentFilterInstance().getFilterValue() || []
          : [...currentValue];

        const safeFilterValue = Array.isArray(currentFilterValue)
          ? currentFilterValue
          : [];

        // Always update both values to ensure they're in sync
        setCurrentValue(safeFilterValue);
        setSelectedValues(safeFilterValue);
      } catch (error) {
        if (process.env.NODE_ENV !== 'production') {
          // eslint-disable-next-line no-console
          console.error('Error getting current filter value:', error);
        }
        setSelectedValues([...currentValue]);
      }

      setHasChanges(false);
      fetchEpicNames('');
      setSearchValue('');
      setIsDropdownOpen(true);
    } else {
      // If dropdown is closing and there's a search value, clear it
      if (searchValue) {
        setSearchValue('');
        fetchEpicNames('');
      }
      // Don't reset selectedValues here as it causes the issue
      setHasChanges(false);
      setIsDropdownOpen(false);
    }
  };

  // Handle search input click to prevent dropdown from closing
  const handleSearchClick = e => {
    e.stopPropagation();
    if (searchInputRef.current) {
      searchInputRef.current.focus();
    }
  };

  // Handle blur event (when clicking outside)
  const handleBlur = e => {
    // Check if the blur was caused by clicking on the Apply button
    const clickedElement = e?.relatedTarget?.closest
      ? e.relatedTarget.closest('*')
      : null;

    const isApplyButton = clickedElement?.textContent === 'Apply';

    // Only prevent default behavior for Apply button
    if (isApplyButton) {
      return;
    }

    // Clear search value if any
    if (searchValue) {
      setSearchValue('');
    }

    // Always reset to current value (which is the last applied value)
    setSelectedValues([...currentValue]);
    setHasChanges(false);

    // Close dropdown if it's open
    if (isDropdownOpen) {
      setIsDropdownOpen(false);
    }

    // Call onBlur prop if provided
    if (props.onBlur) {
      props.onBlur(currentValue);
    }
  };

  // Get placeholder text based on current state
  const getPlaceholderText = () => {
    if (isApplyingFilter) {
      return 'Applying filter...';
    }
    if (currentValue.length > 0) {
      return `${
        currentValue.length
      } epic(s) selected - close dropdown to apply filter`;
    }
    return 'Select epic names...';
  };

  // Scroll tag overflow to top when currentValue changes
  useEffect(() => {
    if (tagOverflowRef.current) {
      tagOverflowRef.current.scrollTop = 0;
    }
  }, [currentValue]);

  // Custom tag renderer to handle overflow and styling
  const tagRender = tagProps => {
    const { label, value, closable, onClose } = tagProps;

    // Find the epic name for this value
    const epic = epicOptions.find(e => e.value === value);
    const displayName = epic ? epic.text : label;

    // Handle tag close with API call
    const handleClose = e => {
      e.preventDefault();
      e.stopPropagation();

      // Remove the tag
      const newValue = currentValue.filter(val => val !== value);
      setCurrentValue(newValue);

      // Apply filter immediately when removing a tag
      applyFilter(newValue);

      // Call the original onClose if provided
      if (onClose) {
        onClose();
      }
    };

    const onPreventMouseDown = event => {
      event.preventDefault();
      event.stopPropagation();
    };

    return (
      <Tooltip title={displayName}>
        <StyledTag
          onMouseDown={onPreventMouseDown}
          closable={closable}
          onClose={handleClose}
        >
          <span
            style={{
              display: 'inline-block',
              maxWidth: '180px',
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              verticalAlign: 'middle',
            }}
          >
            {displayName}
          </span>
        </StyledTag>
      </Tooltip>
    );
  };

  return (
    <SelectWrapper>
      <Select
        mode="multiple"
        placeholder={getPlaceholderText()}
        value={selectedValues}
        onChange={onCustomFilterSelect}
        onSearch={handleSearch}
        onDropdownVisibleChange={handleDropdownVisibleChange}
        onBlur={handleBlur}
        onKeyDown={handleKeyDown}
        onInputKeyDown={e => {
          // Prevent default behavior for Enter key in search input
          if (e.key === 'Enter') {
            e.preventDefault();
            e.stopPropagation();
            return false;
          }
          return undefined;
        }}
        searchValue={searchValue}
        showSearch={false}
        onSelect={() => false} // Prevent selection on Enter
        className="epic-name-filter"
        style={{
          width: 280,
          minWidth: 280,
          maxWidth: 280,
        }}
        suffixIcon={
          <span className="ant-select-arrow">
            <svg
              viewBox="0 0 24 24"
              width="1em"
              height="1em"
              fill="currentColor"
              aria-hidden="true"
            >
              <path d="M7 10l5 5 5-5z" />
            </svg>
          </span>
        }
        tagRender={tagRender}
        filterOption={false} // We handle filtering on server side
        notFoundContent={loading ? 'Loading...' : 'No epic names found'}
        dropdownMatchSelectWidth={false}
        dropdownStyle={{
          width: '350px',
          padding: '8px',
          borderRadius: '8px',
          boxShadow:
            '0 3px 6px -4px rgba(0, 0, 0, 0.12), 0 6px 16px 0 rgba(0, 0, 0, 0.08), 0 9px 28px 8px rgba(0, 0, 0, 0.05)',
        }}
        dropdownRender={menu => (
          <div className="epic-dropdown-container">
            <div
              style={{
                padding: '8px',
                borderBottom: '1px solid #f0f0f0',
              }}
            >
              <input
                ref={searchInputRef}
                className="ant-input"
                placeholder="Search epic names..."
                value={searchValue}
                onChange={e => {
                  setSearchValue(e.target.value);
                  handleSearch(e.target.value);
                }}
                onClick={handleSearchClick}
                style={{
                  width: '100%',
                  padding: '4px 11px',
                  borderRadius: '4px',
                  border: '1px solid #d9d9d9',
                }}
                onKeyDown={handleKeyDown}
              />
            </div>
            <div style={{ maxHeight: '300px', overflowY: 'auto' }}>{menu}</div>
            <div
              style={{
                display: 'flex',
                justifyContent: 'flex-end',
                padding: '8px',
                borderTop: '1px solid #f0f0f0',
                gap: '8px',
              }}
            >
              <button
                type="button"
                onClick={handleCancel}
                style={{
                  padding: '4px 15px',
                  border: '1px solid #d9d9d9',
                  borderRadius: '4px',
                  background: '#fff',
                  cursor: 'pointer',
                }}
              >
                Cancel
              </button>
              <button
                type="button"
                onClick={handleApply}
                style={{
                  padding: '4px 15px',
                  border: '1px solid #1890ff',
                  borderRadius: '4px',
                  background: '#1890ff',
                  color: '#fff',
                  cursor: 'pointer',
                }}
              >
                Apply
              </button>
            </div>
          </div>
        )}
        onClick={e => {
          // Prevent default to avoid focusing the select input
          e.preventDefault();
          e.stopPropagation();
          if (!isDropdownOpen) {
            handleDropdownVisibleChange(true);
          }
        }}
      >
        {epicOptions.map(option => (
          <Option key={option.value} value={option.value} label={option.text}>
            {option.text}
          </Option>
        ))}
      </Select>
    </SelectWrapper>
  );
});

EpicNameMultiSelectFilter.displayName = 'EpicNameMultiSelectFilter';

EpicNameMultiSelectFilter.propTypes = {
  parentFilterInstance: PropTypes.shape({
    getFilterValue: PropTypes.func.isRequired,
  }).isRequired,
  onBlur: PropTypes.func,
};

export default EpicNameMultiSelectFilter;
