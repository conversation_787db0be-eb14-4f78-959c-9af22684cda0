/**
 * Tests for UserLogs
 *
 */

import React from 'react';
import { fireEvent, render, wait } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import 'jest-dom/extend-expect';
import history from 'utils/history';
import { browserHistory } from 'react-router-dom';
import { ConnectedRouter } from 'connected-react-router';
import request from 'utils/request';
import moment from 'moment';
import { UserLogs } from '../index';
import configureStore from '../../../configureStore';
import {
  responseWithZeroList,
  failedResponse,
  responseWithList,
  TEST_IDS,
  TEST_PROPS,
} from '../stub/test.stub';
import { GENERIC_MOMENT_DATE_FORMAT } from '../../constants';
jest.mock('utils/request');
let store;

const componentWrapper = props =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <UserLogs {...props} />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<UserLogs />', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  beforeEach(() => {
    request.mockImplementationOnce(() => Promise.resolve({}));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Should render and match the snapshot', () => {
    const {
      container: { firstChild },
    } = componentWrapper();
    expect(firstChild).toMatchSnapshot();
  });
});

describe('Check calendar  view is being shown properly', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  beforeEach(() => {
    request.mockImplementation(() => Promise.resolve({ data: { logs: [] } }));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('No Records found for logs', async () => {
    request.mockImplementationOnce(() =>
      Promise.resolve(responseWithZeroList()),
    );
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });

  it('Employees with logs', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(1)));
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });

  it('Check if logs can be clicked and if jira url is not present it will not be redirected', async () => {
    const mockFunction = jest.fn();
    global.open = mockFunction;
    // Create a custom response where the first log doesn't have jiraIssueUrl
    const customResponse = {
      ...responseWithList(1),
      data: {
        ...responseWithList(1).data,
        logs: responseWithList(1).data.logs.map((log, index) => {
          if (index === 0) {
            // Remove jiraIssueUrl from the first log
            const { jiraIssueUrl, ...logWithoutJiraUrl } = log;
            return logWithoutJiraUrl;
          }
          return log;
        }),
      },
    };
    request.mockImplementationOnce(() => Promise.resolve(customResponse));
    const { getAllByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getAllByTestId(TEST_IDS.LOG_CLICK)[0]);
    expect(mockFunction).not.toHaveBeenCalled();
  });
  it('Check if logs can be clicked and redirected to jira url', async () => {
    const mockFunction = jest.fn();
    global.open = mockFunction;
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(1)));
    const { getAllByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getAllByTestId(TEST_IDS.LOG_CLICK)[1]);
    expect(mockFunction).toHaveBeenCalled();
  });

  it('Download logs', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(2)));
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    global.URL.createObjectURL = jest.fn();
    global.URL.revokeObjectURL = () => true;
    request.mockImplementationOnce(() =>
      Promise.resolve({
        status: 200,
        blob: () => 'Data',
      }),
    );
    fireEvent.click(getByTestId(TEST_IDS.DOWNLOAD_BTN));
    await wait(() => expect(request).toHaveBeenCalledTimes(2));
  });

  it('Failed Employee api', async () => {
    request.mockImplementationOnce(() => Promise.reject(failedResponse));
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));

    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });
});

describe('Check calendar view with userId props is being shown properly', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  afterEach(() => {
    request.mockReset();
  });

  it('No Records found for logs', async () => {
    request.mockImplementationOnce(() =>
      Promise.resolve(responseWithZeroList()),
    );
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });

  it('Employees with logs', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(3)));
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });

  it('Download logs', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(4)));
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    global.URL.createObjectURL = jest.fn();
    global.URL.revokeObjectURL = () => true;
    request.mockImplementationOnce(() =>
      Promise.resolve({
        status: 200,
        blob: () => 'Data',
      }),
    );
    fireEvent.click(getByTestId(TEST_IDS.DOWNLOAD_BTN));
    await wait(() => expect(request).toHaveBeenCalledTimes(2));
  });

  it('Failed Employee api', async () => {
    request.mockImplementationOnce(() => Promise.reject(failedResponse));
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));

    expect(getByTestId(TEST_IDS.SUMMARY_CONTAINER).childNodes.length).toBe(3);
  });
});

describe('Check calendar click on day updates month', async () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Change calendar view', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList(5)))
      .mockImplementationOnce(() => Promise.resolve(responseWithList(5)));
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.CELL));
  });
});

describe('Sync Logs', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Click on Sync Logs should open Modal', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(3)));
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.SYNC_LOGS_BTN));
    await wait(() =>
      expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument(),
    );
    expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument();
    fireEvent.click(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON));
  });

  it('Click on cancel should close the modal', async () => {
    request.mockImplementationOnce(() => Promise.resolve(responseWithList(3)));
    const { getByTestId } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.SYNC_LOGS_BTN));
    await wait(() =>
      expect(
        getByTestId(TEST_IDS.SYNC_CONFIRMATION_BUTTON),
      ).toBeInTheDocument(),
    );
    expect(getByTestId(TEST_IDS.SYNC_CONFIRMATION_BUTTON)).toBeInTheDocument();
    fireEvent.click(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON));
  });
  it('Open a modal and select the period', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList(3)))
      .mockImplementationOnce(() => Promise.resolve(responseWithList(3)))
      .mockImplementationOnce(() => Promise.resolve(responseWithList(3)));
    const { getByTestId, getByPlaceholderText } = componentWrapper(TEST_PROPS);
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.SYNC_LOGS_BTN));
    await wait(() =>
      expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument(),
    );
    expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument();
    const from = getByPlaceholderText('From');
    // onFocus RangePicker
    fireEvent.focus(from);
    // onBlur RangePicker
    fireEvent.blur(from);
    // onChange from
    fireEvent.mouseDown(from);
    fireEvent.change(from, {
      target: {
        value: moment().format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });
    fireEvent.click(document.querySelectorAll('.ant-picker-cell-selected')[0]);

    // onChange To
    const to = getByPlaceholderText('To');
    fireEvent.mouseDown(to);
    fireEvent.change(to, {
      target: {
        value: moment().format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });
    fireEvent.click(document.querySelectorAll('.ant-picker-cell-selected')[1]);
    fireEvent.click(getByTestId(TEST_IDS.SYNC_CONFIRMATION_BUTTON));
    await wait(() => expect(request).toHaveBeenCalledTimes(3));
    expect(request).toHaveBeenCalledTimes(3);
  });
  it('Open a modal and select the period without user id', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList(3)))
      .mockImplementationOnce(() => Promise.resolve(responseWithList(3)));
    const { getByTestId, getByPlaceholderText } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.SYNC_LOGS_BTN));
    await wait(() =>
      expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument(),
    );
    expect(getByTestId(TEST_IDS.SYNC_CANCEL_BUTTON)).toBeInTheDocument();
    const from = getByPlaceholderText('From');
    // onFocus RangePicker
    fireEvent.focus(from);
    // onBlur RangePicker
    fireEvent.blur(from);
    // onChange from
    fireEvent.mouseDown(from);
    fireEvent.change(from, {
      target: {
        value: moment().format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });
    fireEvent.click(document.querySelectorAll('.ant-picker-cell-selected')[0]);
    // onChange To
    const to = getByPlaceholderText('To');
    fireEvent.mouseDown(to);
    fireEvent.change(to, {
      target: {
        value: moment().format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });
    fireEvent.click(document.querySelectorAll('.ant-picker-cell-selected')[1]);
    fireEvent.click(getByTestId(TEST_IDS.SYNC_CONFIRMATION_BUTTON));
    await wait(() => expect(request).toHaveBeenCalledTimes(3));
    expect(request).toHaveBeenCalledTimes(3);
  });
});

describe('Download failed case', async () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
  });

  afterEach(() => {
    request.mockReset();
  });

  it('download catch block', async () => {
    request
      .mockImplementationOnce(() => Promise.reject(responseWithList(6)))
      .mockImplementationOnce(() =>
        // eslint-disable-next-line prefer-promise-reject-errors
        Promise.reject({ status: 0, message: 'error' }),
      );
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(1));
    fireEvent.click(getByTestId(TEST_IDS.DOWNLOAD_BTN));
    await wait(() => expect(request).toHaveBeenCalledTimes(2));
  });
});

describe('getSingleDateLogsList month/year filtering', () => {
  it('should only include logs from the selected month and year', () => {
    const instance = new UserLogs({});
    // Set calendarValue to April 2025
    instance.state = {
      ...instance.state,
      calendarValue: moment('2025-04-01'),
      logsList: [
        // Log for April 10, 2025
        {
          _id: '1',
          logDate: '2025-04-10T00:00:00.000Z',
          type: 'Present',
          timeSpentHours: 8,
        },
        // Log for May 10, 2025 (should be excluded)
        {
          _id: '2',
          logDate: '2025-05-10T00:00:00.000Z',
          type: 'Present',
          timeSpentHours: 8,
        },
        // Log for April 10, 2024 (should be excluded)
        {
          _id: '3',
          logDate: '2024-04-10T00:00:00.000Z',
          type: 'Present',
          timeSpentHours: 8,
        },
      ],
    };
    // Query for April 10, 2025
    const value = moment('2025-04-10');
    const { listData } = instance.getSingleDateLogsList(value);
    expect(listData.length).toBe(1);
    expect(listData[0]._id).toBe('1');
  });

  it('should return empty if no logs match the selected month/year', () => {
    const instance = new UserLogs({});
    instance.setState({
      calendarValue: moment('2025-04-01'),
      logsList: [
        // Log for May 10, 2025
        {
          _id: '2',
          logDate: '2025-05-10T00:00:00.000Z',
          type: 'Present',
          timeSpentHours: 8,
        },
      ],
    });
    const value = moment('2025-04-10');
    const { listData } = instance.getSingleDateLogsList(value);
    expect(listData.length).toBe(0);
  });
});
