/* eslint-disable no-underscore-dangle */
/**
 *
 * UserLogs
 *
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { Helmet } from 'react-helmet';
import { FormattedMessage } from 'react-intl';
import request from 'utils/request';
import { get } from 'lodash';
import { <PERSON>, withRouter } from 'react-router-dom';
import {
  ArrowLeftOutlined,
  InfoCircleOutlined,
  CheckCircleFilled,
  CloseCircleFilled,
  ClockCircleFilled,
  CheckOutlined,
  ClockCircleOutlined,
  CloseOutlined,
} from '@ant-design/icons';

import {
  notification,
  Space,
  Card,
  Statistic,
  Row,
  Col,
  Calendar,
  Tag,
  Spin,
  Button,
  Tooltip,
  Modal,
  Form,
  DatePicker,
  Alert,
} from 'antd';
import moment from 'moment-timezone';
import messages from './messages';
import {
  StyledDateContainer,
  StyledLegendColor,
  StyledTableDate,
  LegendRow,
  LegendItem,
  RangePickerContainer,
  PageHeaderWrapper,
  StyledUserLogsContainer,
} from './StyledDateContainer';
import {
  LOG_TYPE_PRESENT,
  MAX_SYNC_LOG_DAYS_RANGE,
  SYNC_USER_LOGS_FORM_KEY,
  THREE_MONTH_RANGE_FOR_EACH_MONTH,
} from './constants';
import { TEST_IDS } from './stub/test.stub';
import {
  API_ENDPOINTS,
  GENERIC_MOMENT_DATE_FORMAT,
  ROLES,
  ROUTES,
  WEEKEND,
} from '../constants';
import { ALL_LEAVE_DURATION, LEAVE_TYPES } from '../UserListing/constants';
import { getUserData } from '../../utils/Helper';
import InactiveButtonComponent from '../../components/InactiveButtonComponent';

moment.locale('en', {
  week: {
    dow: 1,
  },
});

const tableBgGreen = 'rgba(77,193,150,1)';
const tableBgYellow = 'rgba(255,255,102, 1)';
const tableBgAmber = 'rgba(255,153,51,1)';
const tableBgRed = 'rgba(255,102,102,1)';
const logStatusGreen = 'rgba(210, 239, 229, 1)';
const logStatusYellow = 'rgba(252, 227, 176, 1)';
const logStatusRed = 'rgba(247, 216, 217, 1)';

const LEGENDS = [
  {
    label: 'No deviation',
    value: tableBgGreen,
  },
  {
    label: 'Deviation',
    value: tableBgRed,
  },
  {
    label: 'More than deviation',
    value: tableBgYellow,
  },
  {
    label: 'Extra working hours',
    value: tableBgAmber,
  },
  {
    label: 'Pending Logs',
    logStatus: 0,
    value: 'orange',
  },
  {
    label: 'Approved Logs',
    logStatus: 1,
    value: 'green',
  },
  {
    label: 'Rejected Logs',
    logStatus: 2,
    value: 'red',
  },
];
const { RangePicker } = DatePicker;

export class UserLogs extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      showNotification: false,
      notificationMessage: '',
      userId: get(props, 'location.state.userId', ''),
      name: get(props, 'location.state.name', ''),
      isActive: get(props, 'location.state.isActive', ''),
      projectId: get(props, 'location.state.projectId', ''),
      showBack: !!get(props, 'location.state.userId', ''),
      pageBackButton: {
        status: get(props, 'location.state.isActive', ''),
        currentPage: get(props, 'location.state.currentPage', ''),
        pageSize: get(props, 'location.state.pageSize', ''),
        searchText: get(props, 'location.state.searchText', ''),
      },
      // Loaders
      downloadLoading: false,
      // Table Loader
      isPaginationLoading: false,
      // Sync Logs Loader
      isSyncLoading: false,

      // Table Data source
      logsList: [],

      panelMode: 'month',
      calendarValue: moment(),

      summary: {
        totalHours: 0,
        approvedLogs: 0,
        rejectedLogs: 0,
        pendingLogs: 0,
        logDeviation: 0,
        punctualityDeviation: 0,
      },
      // Sync Options
      showSyncModal: false,
      syncPeriod: [],
      selectedDates: [],
    };
  }

  componentDidMount() {
    const showNotification = get(
      this.props,
      'location.state.showNotification',
      false,
    );
    if (showNotification) {
      this.getNotification();
    }
    this.getUserLogs(false);
  }

  getPageBackLink = () => {
    let pageBackLink = ROUTES.USERS;
    if (this.state.pageBackButton.currentPage) {
      pageBackLink = `${ROUTES.USERS}?${new URLSearchParams(
        this.state.pageBackButton,
      ).toString()}`;
    }
    return pageBackLink;
  };

  getUserLogs = (isSynced = false) => {
    const { userId, projectId } = this.state;
    this.setState({
      isPaginationLoading: true,
    });
    let URL = `${API_ENDPOINTS.USER_ATTENDANCE}`;
    let firstQueryParam = true;
    const getConnector = () => (firstQueryParam ? '?' : '&');
    if (userId) {
      URL += `${getConnector()}userId=${userId}`;
      firstQueryParam = false;
    }

    if (projectId) {
      URL += `${getConnector()}projectId=${projectId}`;
      firstQueryParam = false;
    }

    let startDate;
    let endDate;
    if (isSynced) {
      const { syncPeriod } = this.state;
      startDate = moment(syncPeriod[0])
        .startOf('month')
        .format(GENERIC_MOMENT_DATE_FORMAT);

      const calendarValue = moment(syncPeriod[1])
        .endOf('month')
        .format(GENERIC_MOMENT_DATE_FORMAT);

      endDate =
        moment(syncPeriod[1]).format('MM') === moment().format('MM')
          ? moment().format(GENERIC_MOMENT_DATE_FORMAT)
          : calendarValue;
    } else {
      const calendarValue = this.state.calendarValue
        .endOf('month')
        .format(GENERIC_MOMENT_DATE_FORMAT);

      endDate =
        this.state.calendarValue.format('MM') === moment().format('MM')
          ? moment().format(GENERIC_MOMENT_DATE_FORMAT)
          : calendarValue;

      startDate = this.state.calendarValue
        .startOf('month')
        .format(GENERIC_MOMENT_DATE_FORMAT);
    }

    URL += `${getConnector()}startDate=${startDate}&endDate=${endDate}`;

    request(URL, {
      method: 'GET',
    })
      .then(res => {
        const newLogs = get(res, 'data.logs', []);
        // Merge logic: create a map of logDate to log for both old and new logs
        const { logsList: oldLogs } = this.state;
        const logMap = {};
        // Add old logs to map
        oldLogs.forEach(log => {
          logMap[log.logDate] = log;
        });
        // Overwrite/add new logs for the fetched range
        newLogs.forEach(log => {
          logMap[log.logDate] = log;
        });
        // If isSynced, only update logs in the fetched range, keep others
        const mergedLogs = Object.values(logMap);
        // Optional: sort logs by date descending
        mergedLogs.sort((a, b) => new Date(b.logDate) - new Date(a.logDate));
        this.setState({
          syncPeriod: [],
          isPaginationLoading: false,
          logsList: mergedLogs,
          summary: get(res, 'data.summary', {
            totalHours: 0,
            approvedLogs: 0,
            rejectedLogs: 0,
            pendingLogs: 0,
            logDeviation: 0,
            punctualityDeviation: 0,
          }),
        });
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        this.setState({
          isPaginationLoading: false,
        });
      });
  };

  isRoleHR = () => getUserData().role === ROLES.HR;

  downloadLogs = () => {
    const { userId } = this.state;
    const userData = getUserData();
    this.setState({
      downloadLoading: true,
    });
    let requestUrl = `${API_ENDPOINTS.LOGS_DOWNLOAD}`;
    let firstQueryParam = true;
    const getConnector = () => (firstQueryParam ? '?' : '&');
    const user = userId || userData._id;
    if (user) {
      requestUrl += `${getConnector()}userId=${user}`;
      firstQueryParam = false;
    }

    requestUrl += `${getConnector()}startDate=${this.state.calendarValue
      .startOf('month')
      .format(
        GENERIC_MOMENT_DATE_FORMAT,
      )}&endDate=${this.state.calendarValue
      .endOf('month')
      .format(GENERIC_MOMENT_DATE_FORMAT)}`;

    request(requestUrl, {
      method: 'GET',
      blob: true,
    })
      .then(res => {
        const url = URL.createObjectURL(res);
        const a = document.createElement('a');
        a.href = url;
        a.download = `${this.state.name || 'download'}.csv`;
        a.style.display = 'none';
        a.target = '_blank';
        document.body.append(a);
        a.click();
        URL.revokeObjectURL(url);
        document.body.removeChild(a);
        notification.success({
          message: <FormattedMessage {...messages.downloadSuccess} />,
          description: res.message,
        });
        this.setState({
          downloadLoading: false,
        });
      })
      .catch(error => {
        notification.error({
          message: <FormattedMessage {...messages.failedToLoad} />,
          description: error.message,
        });
        this.setState({
          downloadLoading: false,
        });
      });
  };

  updateField = value => {
    // Update state
    this.setState({ calendarValue: value }, this.getUserLogs);
  };

  getSingleDateLogsList = value => {
    const workedData = {};
    const selectedMonth = this.state.calendarValue.month();
    const selectedYear = this.state.calendarValue.year();
    const listData = this.state.logsList.filter(log => {
      const valueDate = value.utc().format(GENERIC_MOMENT_DATE_FORMAT);
      const logDate = log.logDate.split('T')[0];
      const logMoment = moment(logDate, GENERIC_MOMENT_DATE_FORMAT);
      // Only include logs for the selected month and year
      if (
        logMoment.month() === selectedMonth &&
        logMoment.year() === selectedYear &&
        logDate === valueDate
      ) {
        if (log.type === LOG_TYPE_PRESENT) {
          workedData[valueDate] =
            get(workedData, valueDate, 0) + log.timeSpentHours;
        }
        return true;
      }
      return false;
    });
    return { workedData, listData };
  };

  getColor = (hours, value, leaveInfo) => {
    const isLeaveTaken = leaveInfo && Object.keys(leaveInfo).length > 0;
    if (isLeaveTaken) {
      // eslint-disable-next-line no-param-reassign
      hours = leaveInfo.timeSpentHours + hours;
    }
    if (WEEKEND.indexOf(value.day()) !== -1) {
      return tableBgAmber;
    }
    if (hours > 8) {
      return tableBgYellow;
    }
    if (hours < 8) {
      return tableBgRed;
    }
    return tableBgGreen;
  };

  getLogColor = log => {
    if (log.logStatus === 1) {
      return logStatusGreen;
    }
    if (log.logStatus === 2) {
      return logStatusRed;
    }
    return logStatusYellow;
  };

  getLeaveData = listData => {
    const filteredLeaves = listData.filter(log =>
      LEAVE_TYPES.some(leaveType => leaveType.label === log.type),
    );
    if (filteredLeaves.length > 0) {
      return { leaveData: filteredLeaves };
    }
    return false;
  };

  getHoursUI = (hours, value, leaveInfo) => (
    <li>
      <Tag
        className="calendarTagStyling"
        color={this.getColor(hours, value, leaveInfo)}
        title={`${hours}h total`}
      >
        {hours}h total
      </Tag>
    </li>
  );

  getLeaveDuration = duration => {
    const leaveDuration = ALL_LEAVE_DURATION.filter(
      leaveType => leaveType.value === duration,
    );
    return leaveDuration && leaveDuration.length > 0 && leaveDuration[0].label;
  };

  getNotification = () => {
    const URL = `${API_ENDPOINTS.KRA_NOTIFICATION}`;

    request(URL, {
      method: 'GET',
    })
      .then(res => {
        this.setState({
          notificationMessage: get(res, 'data.message', ''),
          showNotification: true,
        });
      })
      .catch(() => {
        this.setState({
          showNotification: false,
        });
      });
  };

  /**
   * handleLogClick
   * @param {object} logData
   * opens log url in new tab
   */
  handleLogClick = logData => {
    if (logData.jiraIssueUrl) {
      window.open(logData.jiraIssueUrl, '_blank');
    }
  };

  dateCellRender = value => {
    const { listData, workedData } = this.getSingleDateLogsList(value);
    const hours = workedData[value.format(GENERIC_MOMENT_DATE_FORMAT)];
    const isLeaveTaken = this.getLeaveData(listData);
    const leaveInfo = isLeaveTaken && isLeaveTaken.leaveData[0];
    const leaveInfoArray = isLeaveTaken?.leaveData || [];
    return (
      <StyledDateContainer data-testid={TEST_IDS.CELL}>
        {leaveInfoArray.length > 0 &&
          leaveInfoArray.map(leave => (
            <li>
              <Tag
                title={`${leave.type} (${this.getLeaveDuration(
                  leave.duration,
                )})`}
                className="calendarTagStyling leaveData"
              >
                <p>{leave.type}</p>
                {leave.type !== LEAVE_TYPES[5].label && (
                  <p>&nbsp;{`(${this.getLeaveDuration(leave.duration)})`}</p>
                )}
              </Tag>
            </li>
          ))}
        {hours && this.getHoursUI(hours, value, leaveInfo)}
        {listData.map(log => (
          <li key={log._id}>
            {log.type === LOG_TYPE_PRESENT && (
              <Tag
                data-testid={TEST_IDS.LOG_CLICK}
                className="calendarTagStyling logDetail"
                color={this.getLogColor(log)}
                title={`${log.jiraProjectName}\n${log.jiraTitle}\n${
                  log.timeSpentHours
                } hours\n${log.daysDeviation} deviation day(s)`}
                onClick={() => this.handleLogClick(log)}
              >
                <span className="u-d-flex">
                  <span className="projectNameEllipsis">
                    {log.jiraProjectName}
                  </span>
                  {log.timeSpentHours}h ({log.daysDeviation})
                </span>
                {log.logStatus === 0 && (
                  <ClockCircleFilled
                    style={{ fontSize: '14px', color: 'orange' }}
                  />
                )}
                {log.logStatus === 1 && (
                  <CheckCircleFilled
                    style={{ fontSize: '14px', color: 'green' }}
                  />
                )}
                {log.logStatus === 2 && (
                  <CloseCircleFilled
                    style={{ fontSize: '14px', color: 'red' }}
                  />
                )}
              </Tag>
            )}
          </li>
        ))}
      </StyledDateContainer>
    );
  };

  onPanelChange = (value, panel) => {
    this.setState({ panelMode: panel });
    if (panel === 'month') {
      this.updateField(value.utc());
    }
  };

  showSyncOptionModal = () => {
    this.setState({
      showSyncModal: true,
    });
  };

  /**
   * Toggle Sync Logs Modal
   */
  toggleModal = () => {
    const { showSyncModal } = this.state;

    this.setState({
      showSyncModal: !showSyncModal,
      syncPeriod: [],
      selectedDates: [],
    });
  };

  syncLogs = () => {
    const { showSyncModal, syncPeriod, userId, name } = this.state;
    this.setState(
      {
        isSyncLoading: true,
      },
      () => {
        let URL = `${API_ENDPOINTS.SYNC_LOGS}?startDate=${moment(
          syncPeriod[0],
        ).format(GENERIC_MOMENT_DATE_FORMAT)}&endDate=${moment(
          syncPeriod[1],
        ).format(GENERIC_MOMENT_DATE_FORMAT)}`;

        if (userId.length > 0 && name.length > 0) {
          URL += `&userIds=${userId}`;
        }
        request(URL, { method: 'GET' })
          .then(res => {
            this.setState(
              {
                isSyncLoading: false,
                showSyncModal: !showSyncModal,
                selectedDates: [],
              },
              () => {
                this.getUserLogs(true);
              },
            );
            notification.success({
              message: <FormattedMessage {...messages.syncingLogs} />,
              description: res.message,
            });
          })
          .catch(error => {
            notification.error({
              message: <FormattedMessage {...messages.failedNewJiraRequest} />,
              description: error.message,
            });
            this.setState({
              isSyncLoading: false,
            });
          });
      },
    );
  };

  /**
   * handleSyncPeriodChange
   * @param {*} e
   */
  handleSyncPeriodChange = e => {
    this.setState({
      syncPeriod: e,
    });
  };

  getDisabledDates = current => {
    const { selectedDates } = this.state;
    if (current) {
      // min condition - Min 3 months before date can be selected
      if (
        !THREE_MONTH_RANGE_FOR_EACH_MONTH[moment().month()].includes(
          current.month(),
        )
      ) {
        return true;
      }

      // max condition - No future date can be selected
      if (current > moment()) {
        return true;
      }

      // dynamic condition for ranges ( max 31 days can be selected)
      if (!selectedDates || selectedDates.length === 0) {
        return false;
      }

      // First Date - Disable all the dates after 31 days
      const tooLate =
        selectedDates[0] &&
        current.diff(selectedDates[0], 'days') >= MAX_SYNC_LOG_DAYS_RANGE;

      // Second Date - Disable all the dates before 31 days
      const tooEarly =
        selectedDates[1] &&
        selectedDates[1].diff(current, 'days') >= MAX_SYNC_LOG_DAYS_RANGE;
      return tooEarly || tooLate;
    }
    return false;
  };

  /**
   * Modal for sync
   * @returns {Modal}  form content
   */
  syncOptionModal = () => {
    const { showSyncModal, isSyncLoading, syncPeriod } = this.state;
    const periodExist = !Array.isArray(syncPeriod);
    const invalidRange = periodExist || syncPeriod.length !== 2;
    const isInvalidDateRange =
      invalidRange ||
      !moment.isMoment(syncPeriod[0]) ||
      !moment.isMoment(syncPeriod[1]);

    const performingAction = isSyncLoading || isInvalidDateRange;
    return (
      <Modal
        title="Sync Logs"
        visible={showSyncModal}
        onOk={this.syncLogs}
        confirmLoading={isSyncLoading}
        onCancel={() => this.toggleModal()}
        okButtonProps={{
          disabled: performingAction,
          'data-testid': TEST_IDS.SYNC_CONFIRMATION_BUTTON,
        }}
        okText={<FormattedMessage {...messages.syncLabel} />}
        cancelButtonProps={{
          disabled: isSyncLoading,
          'data-testid': TEST_IDS.SYNC_CANCEL_BUTTON,
        }}
      >
        <RangePickerContainer>
          <Form onSubmit={this.syncLogs} name={SYNC_USER_LOGS_FORM_KEY}>
            <RangePicker
              format={GENERIC_MOMENT_DATE_FORMAT}
              label="Period"
              value={syncPeriod}
              defaultPickerValue={[moment(), moment()]}
              name="period"
              placeholder={['From', 'To']}
              hasFeedback
              onFocus={e => e.preventDefault()}
              onBlur={e => e.preventDefault()}
              onChange={e => this.handleSyncPeriodChange(e)}
              onCalendarChange={e => this.setState({ selectedDates: e })}
              disabledDate={this.getDisabledDates}
            />
          </Form>
        </RangePickerContainer>
      </Modal>
    );
  };

  legendRenderer = () => (
    <LegendRow>
      {LEGENDS.map(legend => (
        <LegendItem className="m-2" key={legend.label}>
          {!legend.logStatus && legend.logStatus !== 0 && (
            <StyledLegendColor
              color={legend.value}
              className="legendWithIcon"
            />
          )}
          {legend.logStatus >= 0 && (
            <StyledLegendColor
              color={legend.value}
              className={
                legend.logStatus === 0
                  ? 'legendWithIcon pending'
                  : 'legendWithIcon'
              }
            >
              {legend.logStatus === 0 && (
                <ClockCircleOutlined
                  style={{ fontSize: '16px', color: 'white' }}
                />
              )}
              {legend.logStatus === 1 && (
                <CheckOutlined style={{ fontSize: '10px', color: 'white' }} />
              )}
              {legend.logStatus === 2 && (
                <CloseOutlined style={{ fontSize: '10px', color: 'white' }} />
              )}
            </StyledLegendColor>
          )}
          <span>{legend.label}</span>
        </LegendItem>
      ))}
    </LegendRow>
  );

  render() {
    const {
      isPaginationLoading,
      logsList,
      showBack,
      name,
      userId,
      summary,
      isActive,
      showNotification,
      notificationMessage,
    } = this.state;
    const hoursLabel =
      logsList.length && summary.totalHours > 1 ? 'hours' : 'hour';
    return (
      <StyledUserLogsContainer data-test-id={TEST_IDS.MAIN_WRAPPER}>
        <Helmet>
          <title>Logs</title>
          <meta name="description" content="Logs" />
        </Helmet>
        {showNotification && notificationMessage && (
          <Alert message={notificationMessage} type="warning" showIcon />
        )}
        <PageHeaderWrapper
          title={
            <Space>
              {showBack && (
                <Link to={this.getPageBackLink()}>
                  <ArrowLeftOutlined />
                </Link>
              )}
              <span>
                Logs
                {name && <span> for {name}</span>}
              </span>
            </Space>
          }
          className="site-page-header"
          extra={[
            <Row gutter={5} key="1">
              <Col>
                <Button
                  data-testid={TEST_IDS.SYNC_LOGS_BTN}
                  key="Button_1"
                  type="primary"
                  onClick={this.showSyncOptionModal}
                  title="Sync Logs"
                >
                  Sync Logs
                </Button>
              </Col>
              <Col>
                <InactiveButtonComponent
                  visible={this.isRoleHR()}
                  key="Button_2"
                  showAsBox
                  data={{
                    _id: userId,
                    firstName: name.split(' ')[0],
                    lastName: name.split(' ')[1],
                    isActive,
                  }}
                />
              </Col>
              <Col>
                <Button
                  data-testid={TEST_IDS.DOWNLOAD_BTN}
                  key="Button_3"
                  type="primary"
                  onClick={this.downloadLogs}
                  loading={this.state.downloadLoading}
                  title="Download"
                >
                  Download
                </Button>
              </Col>
            </Row>,
          ]}
        >
          <Space
            data-testid={TEST_IDS.SUMMARY_CONTAINER}
            direction="vertical"
            size="large"
            className="w-100"
          >
            <div>
              <Card title={<b>Summary</b>} size="small">
                <Row gutter={16}>
                  <Col span={4}>
                    <Statistic
                      title="Number of hours worked"
                      value={this.state.summary.totalHours}
                      suffix={<span className="text-small">{hoursLabel}</span>}
                      loading={isPaginationLoading}
                    />
                  </Col>
                  <Col span={4}>
                    <Statistic
                      title="Number of approved hours"
                      value={this.state.summary.approvedLogs}
                      valueStyle={{ color: 'green' }}
                      suffix={<span className="text-small">{hoursLabel}</span>}
                      loading={isPaginationLoading}
                    />
                  </Col>
                  <Col span={4}>
                    <Statistic
                      title="Number of rejected hours"
                      value={this.state.summary.rejectedLogs}
                      valueStyle={{ color: 'red' }}
                      suffix={<span className="text-small">{hoursLabel}</span>}
                      loading={isPaginationLoading}
                    />
                  </Col>
                  <Col span={4}>
                    <Statistic
                      title="Number of pending hours"
                      value={this.state.summary.pendingLogs}
                      valueStyle={{ color: 'orange' }}
                      suffix={<span className="text-small">{hoursLabel}</span>}
                      loading={isPaginationLoading}
                    />
                  </Col>
                  <Col span={4}>
                    <Statistic
                      title="Overall deviation"
                      value={this.state.summary.logDeviation}
                      precision={2}
                      suffix="%"
                      loading={isPaginationLoading}
                    />
                  </Col>
                  <Col span={4}>
                    <Statistic
                      title={
                        <span>
                          Non punctuality
                          <Tooltip title="The higher the number, the more irregularity in logging your hours.">
                            <InfoCircleOutlined />
                          </Tooltip>
                        </span>
                      }
                      value={this.state.summary.punctualityDeviation}
                      loading={isPaginationLoading}
                    />
                  </Col>
                </Row>
              </Card>
            </div>
            <StyledTableDate>
              <Spin spinning={isPaginationLoading}>
                <Calendar
                  className=""
                  validRange={[moment('2020-3-1'), moment()]}
                  dateCellRender={this.dateCellRender}
                  onSelect={() => {}}
                  onPanelChange={this.onPanelChange}
                  mode={this.state.panelMode}
                />
              </Spin>
            </StyledTableDate>
            {this.legendRenderer()}
          </Space>
        </PageHeaderWrapper>
        {this.syncOptionModal()}
      </StyledUserLogsContainer>
    );
  }
}

UserLogs.propTypes = {
  userId: PropTypes.string,
  projectId: PropTypes.string,
  period: PropTypes.array,
  location: PropTypes.object,
};

export default withRouter(UserLogs);
