import moment from 'moment';
import { LOG_TYPE_PRESENT } from '../constants';
import { ALL_LEAVE_DURATION, LEAVE_TYPES } from '../../UserListing/constants';

export const responseWithZeroList = () => ({
  data: [],
});

const randomNumber = (current, maxPads, repeatWith) =>
  `${current}`.padEnd(maxPads, repeatWith);

const generateData = () => {
  const listData = [];
  for (let i = 0; i < 20; i += 1) {
    if (i !== 11) {
      listData.push({
        _id: `${randomNumber('606c8028af6699593f93e', 24, i)}`,
        userId: '606c534e64b9b66ef738db17',
        projectId: '606c7e35af6699593f93e379',
        logDate: moment()
          .startOf('month')
          .add(i + 1, 'days')
          .toISOString(),
        projectName: 'GrowExx',
        timeSpentHours: i % 2 === 0 ? 8 : 4,
        daysDeviation: i % 2 === 0 ? 1 : -1,
        type: i % 2 === 0 ? LEAVE_TYPES[0].label : LOG_TYPE_PRESENT,
        duration: ALL_LEAVE_DURATION[0].label,
        jiraProjectName: 'GrowExx',
        jiraIssueUrl: 'https://growexx.atlassian.net/',
      });
    }

    // half day leave plus half day worked
    if (i === 5) {
      listData.push({
        _id: `${randomNumber('606c8028af6699593f93e', 24, i)}`,
        userId: '606c534e64b9b66ef738db17',
        projectId: '606c7e35af6699593f93e379',
        logDate: moment()
          .startOf('month')
          .add(i + 1, 'days')
          .toISOString(),
        projectName: 'GrowExx',
        timeSpentHours: 4,
        daysDeviation: 1,
        type: LEAVE_TYPES[0].label,
        duration: ALL_LEAVE_DURATION[0].value,
      });
    }

    // half day leave plus worked less than half day
    if (i === 7) {
      listData.push({
        _id: `${randomNumber('606c8028af6699593f93e', 24, i)}`,
        userId: '606c534e64b9b66ef738db17',
        projectId: '606c7e35af6699593f93e379',
        logDate: moment()
          .startOf('month')
          .add(i + 1, 'days')
          .toISOString(),
        projectName: 'GrowExx',
        timeSpentHours: 3,
        daysDeviation: 1,
        type: LEAVE_TYPES[0].label,
        duration: ALL_LEAVE_DURATION[2].label,
      });
    }
    // hours > 8
    if (i === 8) {
      listData.push({
        _id: `${randomNumber('606c8028af6699593f93e', 24, i)}`,
        userId: '606c534e64b9b66ef738db17',
        projectId: '606c7e35af6699593f93e379',
        logDate: moment()
          .startOf('month')
          .add(i + 1, 'days')
          .toISOString(),
        projectName: 'GrowExx',
        timeSpentHours: 10,
        daysDeviation: 1,
        type: LOG_TYPE_PRESENT,
        duration: ALL_LEAVE_DURATION[1].value,
      });
    }
    // hours = 8 and log type : present
    if (i === 11) {
      listData.push(
        {
          _id: `${randomNumber('606c8028af6699593f93e', 24, i)}`,
          userId: '606c534e64b9b66ef738db17',
          projectId: '606c7e35af6699593f93e379',
          logDate: moment()
            .startOf('month')
            .toISOString(),
          projectName: 'GrowExx',
          timeSpentHours: 8,
          daysDeviation: 1,
          type: LOG_TYPE_PRESENT,
          duration: ALL_LEAVE_DURATION[0].value,
        },
        {
          _id: `${randomNumber('606c8028af6699593f93e', 30, i)}`,
          userId: '606c534e64b9b66ef738db17',
          projectId: '606c7e35af6699593f93e379',
          logDate: moment()
            .startOf('month')
            .toISOString(),
          projectName: 'GrowExx',
          timeSpentHours: 8,
          daysDeviation: 1,
          type: LOG_TYPE_PRESENT,
          duration: ALL_LEAVE_DURATION[0].value,
          jiraProjectName: 'GrowExx',
          jiraIssueUrl: 'https://growexx.atlassian.net/',
        },
      );
    }
  }

  return listData;
};

export const responseWithList = calls => {
  const docs = generateData();

  return {
    data: {
      logs: docs,
      summary: {
        totalHours: calls % 2 === 0 ? 1 : 2,
        logDeviation: 0,
        punctualityDeviation: 0,
      },
    },
    message: 'Success',
    status: 1,
  };
};

export const failedResponse = {
  data: null,
  status: 0,
  message: 'You do not have access',
};

export const TEST_PROPS = {
  location: {
    state: {
      userId: '606c534e64b9b66ef738db17',
      name: 'John',
      projectId: '606c534e64b9b66ef738db17',
    },
  },
};

export const TEST_IDS = {
  MAIN_WRAPPER: 'MAIN_WRAPPER',
  CELL: 'CELL',
  SUMMARY_CONTAINER: 'SUMMARY_CONTAINER',
  LOG_CLICK: 'LOG_CLICK',
  DOWNLOAD_BTN: 'DOWNLOAD_BTN',

  SYNC_LOGS_BTN: 'SYNC_LOGS_BTN',
  SYNC_CONFIRMATION_BUTTON: 'SYNC_CONFIRMATION_BUTTON',
  SYNC_CANCEL_BUTTON: 'SYNC_CANCEL_BUTTON',
};
