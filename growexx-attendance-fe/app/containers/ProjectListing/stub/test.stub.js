import { DEFAULT_LIMIT, DEFAULT_PAGE } from '../../constants';
import { PROJECT_TABLE_COLUMN_INDEX } from '../constants';

export const responseWithZeroList = () => ({
  data: {
    docs: [],
    totalDocs: 0,
    limit: 0,
    totalPages: 0,
    page: 0,
    pagingCounter: 0,
    hasPrevPage: false,
    hasNextPage: false,
    prevPage: null,
    nextPage: null,
  },
});

const randomNumber = (current, maxPads, repeatWith) =>
  `${current}`.padEnd(maxPads, repeatWith);

const generateProjectData = () => {
  const listData = [];
  listData.push(
    {
      _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
      [PROJECT_TABLE_COLUMN_INDEX[1]]: 'GAS',
      [PROJECT_TABLE_COLUMN_INDEX[2]]: [
        {
          _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
          empId: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
          empRole: 'Data Engineer',
          firstName: 'John',
          lastName: 'Doe',
        },
        {
          _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
          empId: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
          empRole: '',
          firstName: 'John',
          lastName: 'Doe',
        },
      ],
      [PROJECT_TABLE_COLUMN_INDEX[3]]: {
        _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
        firstName: 'John',
        lastName: 'Doe',
      },
      [PROJECT_TABLE_COLUMN_INDEX[4]]: {},
      [PROJECT_TABLE_COLUMN_INDEX[5]]: '12-12-2021',
      [PROJECT_TABLE_COLUMN_INDEX[6]]: '',
      [PROJECT_TABLE_COLUMN_INDEX[7]]: 'Active',
      [PROJECT_TABLE_COLUMN_INDEX[9]]: '640eb22aabbb90563ae44445',
    },
    {
      _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
      [PROJECT_TABLE_COLUMN_INDEX[1]]: 'GAS',
      [PROJECT_TABLE_COLUMN_INDEX[2]]: [],
      [PROJECT_TABLE_COLUMN_INDEX[3]]: {
        _id: `${randomNumber('606c534e64b9b66ef738d', 24, 20)}`,
        firstName: 'John',
        lastName: 'Doe',
      },
      [PROJECT_TABLE_COLUMN_INDEX[4]]: {},
      [PROJECT_TABLE_COLUMN_INDEX[5]]: '12-12-2021',
      [PROJECT_TABLE_COLUMN_INDEX[6]]: '',
      [PROJECT_TABLE_COLUMN_INDEX[7]]: 'Active',
      [PROJECT_TABLE_COLUMN_INDEX[9]]: '640eb22aabbb90563ae44445',
      configuration: {
        repositories: [],
      },
    },
  );
  for (let i = 0; i < 20; i += 1) {
    listData.push({
      _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
      [PROJECT_TABLE_COLUMN_INDEX[1]]: 'GAS',
      [PROJECT_TABLE_COLUMN_INDEX[2]]: [
        {
          _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
          empId: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
          empRole: 'Data Engineer',
          firstName: 'John',
          lastName: 'Doe',
        },
        {
          _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
          empId: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
          empRole: '',
          firstName: 'John',
          lastName: 'Doe',
        },
      ],
      [PROJECT_TABLE_COLUMN_INDEX[3]]: {
        _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
        firstName: 'John',
        lastName: 'Doe',
      },
      [PROJECT_TABLE_COLUMN_INDEX[4]]: {
        _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
        firstName: 'John',
        lastName: 'Doe',
      },
      [PROJECT_TABLE_COLUMN_INDEX[5]]: '12-12-2021',
      [PROJECT_TABLE_COLUMN_INDEX[6]]: '12-12-2022',
      [PROJECT_TABLE_COLUMN_INDEX[7]]: 'Active',
      [PROJECT_TABLE_COLUMN_INDEX[9]]: 'Active',
      configuration: {
        repositories: [],
      },
    });
  }

  return listData;
};

export const generateBUData = () => {
  const listData = [];
  listData.push(
    {
      _id: '640eb22aabbb90563ae44445',
      name: 'Web & Mobile',
    },
    {
      _id: '640eb22aabbb90563ae44446',
      name: 'Data Science',
    },
  );
  return {
    data: listData,
    message: 'Success',
    status: 1,
  };
};

const generateUsersData = () => {
  const listData = [];
  listData.push({
    isActive: 1,
    _id: `${randomNumber('606c534e64b9b66ef738d', 24, 6)}`,
    employeeId: 6,
    firstName: 'Jane',
    lastName: 'Smith',
    email: `<EMAIL>`,
    role: 4,
    label: ['jane.smith'],
  });
  for (let i = 0; i < 5; i += 1) {
    listData.push({
      isActive: 1,
      _id: `${randomNumber('606c534e64b9b66ef738d', 24, i)}`,
      employeeId: i,
      firstName: 'John',
      lastName: 'Doe',
      email: `john.doe${i}@growexx.com`,
      role: 4,
      label: ['john.doe'],
    });
  }

  return listData;
};

export const responseWithList = () => {
  const docs = generateProjectData();

  return {
    data: {
      docs,
      totalDocs: docs.length,
      limit: DEFAULT_LIMIT,
      totalPages: Math.ceil(docs.length / DEFAULT_PAGE),
      page: 1,
      pagingCounter: DEFAULT_PAGE,
      hasPrevPage: false,
      hasNextPage: false,
      prevPage: null,
      nextPage: null,
    },
    message: 'Success',
    status: 1,
  };
};

export const responseWithUsersList = () => {
  const docs = generateUsersData();

  return {
    data: docs,
    message: 'Success',
    status: 1,
  };
};
export const failedResponse = {
  data: null,
  status: 0,
  message: 'You do not have access',
};

export const failureWithPromise = {
  response: {
    json: () => Promise.resolve({ status: 0 }),
  },
  status: 0,
  message: 'You do not have access',
};

export const TEST_IDS = {
  ADD_PROJECT: 'ADD_PROJECT',
  REVIEW_MANAGER_NAME: 'REVIEW_MANAGER_NAME',
  PROJECT_START_DATE: 'PROJECT_START_DATE',
  PROJECT_END_DATE: 'PROJECT_END_DATE',
  INDIVIDUAL_EDIT_BUTTON: 'INDIVIDUAL_EDIT_BUTTON',
  INDIVIDUAL_EDIT_STATE_BUTTON: 'INDIVIDUAL_EDIT_STATE_BUTTON',
  REMOVE_EMPLOYEE_BUTTON: 'REMOVE_EMPLOYEE',
  ADD_PROJECT_BUTTON: 'ADD_PROJECT',
  REMOVE_PROJECT_MANAGER_BUTTON: 'REMOVE_PROJECT_MANAGER_BUTTON',
  ADD_PROJECT_MANAGER_BUTTON: 'ADD_PROJECT_MANAGER_BUTTON',
  ADD_PROJECT_SUBMIT_BUTTON: 'ADD_PROJECT_SUBMIT_BUTTON',
  ADD_PROJECT_CANCEL_BUTTON: 'ADD_PROJECT_CANCEL_BUTTON',
  SEARCH_PROJECT: 'SEARCH_PROJECT',
  CHANGE_PROJECT_STATE_SUBMIT_BUTTON: 'CHANGE_PROJECT_STATE_SUBMIT_BUTTON',
  CHANGE_PROJECT_STATE_CANCEL_BUTTON: 'CHANGE_PROJECT_STATE_CANCEL_BUTTON',
  REVIEW_MANAGER_CHECKBOX: 'REVIEW_MANAGER_CHECKBOX',
};

export const stubSearchValue = {
  target: {
    value: 'Stub Project',
  },
};
