/* eslint-disable indent */
/* eslint-disable no-underscore-dangle */
/**
 *
 * ProjectListing
 *
 */

import React, { PureComponent } from 'react';
import PropTypes from 'prop-types';
import { connect } from 'react-redux';
import { withRouter } from 'react-router-dom';
import { Helmet } from 'react-helmet';
import { FormattedMessage } from 'react-intl';
import request from 'utils/request';
import { debounce, get } from 'lodash';
import { createStructuredSelector } from 'reselect';
import { compose } from 'redux';
import { Field, reduxForm, change } from 'redux-form';
import useInjectReducer from 'utils/injectReducer';
import * as formValidations from 'utils/formValidations';
import {
  PageHeader,
  Table,
  notification,
  Space,
  Tag,
  Button,
  Modal,
  Form,
  Input,
  Tooltip,
  Checkbox,
  Divider,
} from 'antd';
import {
  SearchOutlined,
  EditOutlined,
  MinusOutlined,
  SwapOutlined,
  PlusOutlined,
} from '@ant-design/icons';
import moment from 'moment';
import makeSelectConnectedJiraList from './selectors';
import reducer from './reducer';
import * as actions from './actions';
import {
  isValidLength,
  PROJECT_LIST_FORM_KEY,
  PROJECT_STATES,
  PROJECT_TABLE_COLUMN_INDEX,
} from './constants';
import { TEST_IDS } from './stub/test.stub';
import messages from './messages';
import {
  API_ENDPOINTS,
  DEFAULT_LIMIT,
  DEFAULT_PAGE,
  GET_SORT_ORDER,
  GENERIC_MOMENT_DATE_FORMAT,
  SORTING,
  ROLES,
} from '../constants';
import { ADatePicker, AInput, ASelect } from '../../utils/Fields';
import { setDeepLinkURL } from '../../utils/functions';
import { StyledProjectListingContainer } from './StyledProjectListing';
import { getUserData } from '../../utils/Helper';
import { getCurrentQuarterYear } from '../../components/MyKRA/constants';

const logsTableProps = {
  bordered: true,
  size: 'default',
  title: undefined,
  showHeader: true,
};

export class ProjectListing extends PureComponent {
  constructor(props) {
    super(props);
    this.state = {
      // Loaders
      isLoading: false,
      isChangeStateFormLoading: false,
      // Modal
      showProjectFormModal: false,
      showChangeStateFormModal: false,
      // Table Loader
      isPaginationLoading: false,
      // Table Data source
      projectList: [],
      // Employee List
      employeesList: [],
      // BU List
      buList: [],
      // Integration Settings
      githubToken: '',
      sonarToken: '',
      organization: '',
      // Email Notifications
      emails: [''],
      // Repositories
      repositories: [
        {
          repository: '',
          branch: '',
          sonarKey: '',
          isSwaggerApplicable: false,
          isSonarApplicable: false,
          isCoverageApplicable: false,
        },
      ],
      // Validation
      repositoryErrors: {},
      // Pagination
      pagination: {
        current: DEFAULT_PAGE,
        pageSize: DEFAULT_LIMIT,
        total: 0,
      },
      sortType: SORTING.ASC,
      sortBy: 'projectName',
      searchValue: '',
      hasReviewManager: false,
      currentProjectData: [],
    };
  }

  componentDidMount() {
    const query = new URLSearchParams(window.location.search);

    const searchText = query.get('searchText');
    const currentPage = +query.get('currentPage');
    const queryParamsObj = {};
    if (currentPage) {
      queryParamsObj.pagination = { current: currentPage };
      this.setState({
        pagination: {
          pageSize: 10,
          current: currentPage || 1,
        },
      });
    }
    if (searchText) {
      queryParamsObj.searchValue = searchText;
      this.setState({
        searchValue: searchText,
      });
    }
    this.getProjectList(queryParamsObj);
    this.getEmployeesList();
    this.getBUList();
  }

  checkIfUserIsAdminOrBU = () => {
    if (getUserData().role === ROLES.HR || getUserData().role === ROLES.BU) {
      return true;
    }
    return false;
  };

  getProjectList = ({
    pagination: newPagination,
    sortType: newSortType,
    sortBy: newSortBy,
    searchValue: newSearchValue,
  } = {}) => {
    let { pagination, sortType, sortBy, searchValue } = this.state;
    pagination = newPagination || pagination;
    sortType = newSortType || sortType;
    sortBy = newSortBy || sortBy;
    searchValue = newSearchValue || searchValue;
    const projectNameFilter = encodeURIComponent(searchValue);

    this.setState(
      {
        isPaginationLoading: true,
      },
      () => {
        const URL = `${API_ENDPOINTS.PROJECT_LIST}?page=${
          pagination.current
        }&limit=${
          pagination.pageSize
        }&sort=${sortType}&sortBy=${sortBy}&name=${projectNameFilter}`;

        request(URL, {
          method: 'GET',
        })
          .then(res => {
            this.setState({
              isPaginationLoading: false,
              projectList: get(res, 'data.docs', []),
              pagination: {
                ...pagination,
                total: get(res, 'data.totalDocs', 0),
              },
              sortType,
            });
          })
          .catch(error => {
            notification.error({
              message: <FormattedMessage {...messages.failedToLoad} />,
              description: error.message,
            });
            this.setState({
              isPaginationLoading: false,
            });
          });
      },
    );
  };

  getEmployeesList = () => {
    this.setState(
      {
        isPaginationLoading: true,
      },
      () => {
        const URL = API_ENDPOINTS.EMPLOYEES_LIST;

        request(URL, {
          method: 'GET',
        })
          .then(res => {
            this.setState({
              isPaginationLoading: false,
              employeesList: get(res, 'data', []).map(record => ({
                label: `${record.firstName} ${record.lastName}`,
                value: record._id,
              })),
            });
          })
          .catch(error => {
            notification.error({
              message: <FormattedMessage {...messages.failedToLoad} />,
              description: error.message,
            });
            this.setState({
              isPaginationLoading: false,
            });
          });
      },
    );
  };

  getBUList = () => {
    this.setState(
      {
        isPaginationLoading: true,
      },
      () => {
        const URL = API_ENDPOINTS.BU_LIST;
        request(URL, {
          method: 'GET',
        })
          .then(res => {
            this.setState({
              isPaginationLoading: false,
              buList: get(res, 'data', []).map(record => ({
                label: `${record.name}`,
                value: record._id,
              })),
            });
          })
          .catch(error => {
            notification.error({
              message: <FormattedMessage {...messages.failedToLoad} />,
              description: error.message,
            });
            this.setState({
              isPaginationLoading: false,
            });
          });
      },
    );
  };

  getDisabledDates = current =>
    current && current < moment(this.props.projectStoreData.startDate);

  getColumnProps = () => [
    {
      title: 'Project Name',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[1],
      key: PROJECT_TABLE_COLUMN_INDEX[1],
      width: '15%',
      sorter: true,
      sortDirections: ['descend', 'ascend', 'descend'],
    },
    {
      title: 'Dev(s)',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[2],
      key: PROJECT_TABLE_COLUMN_INDEX[2],
      width: '15%',
      render: empList => (
        <>
          {empList.length > 0 ? (
            empList.map(emp =>
              emp.empRole ? (
                <Tag color="purple" key={emp._id}>{`${emp.firstName} ${
                  emp.lastName
                } : ${emp.empRole}`}</Tag>
              ) : (
                <Tag color="purple" key={emp._id}>{`${emp.firstName} ${
                  emp.lastName
                } :     -`}</Tag>
              ),
            )
          ) : (
            <MinusOutlined />
          )}
        </>
      ),
    },
    {
      title: 'Reporting Manager',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[3],
      key: PROJECT_TABLE_COLUMN_INDEX[3],
      width: '15%',
      render: reportingManager => (
        <>
          {reportingManager.firstName ? (
            <Tag>{`${reportingManager.firstName} ${reportingManager.lastName ||
              ''}`}</Tag>
          ) : (
            <MinusOutlined />
          )}
        </>
      ),
    },
    {
      title: 'Review Manager',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[4],
      key: PROJECT_TABLE_COLUMN_INDEX[4],
      width: '15%',
      render: reviewManager => (
        <>
          {reviewManager._id ? (
            <Tag>{`${reviewManager.firstName} ${reviewManager.lastName}`}</Tag>
          ) : (
            <MinusOutlined />
          )}
        </>
      ),
    },
    {
      title: 'Start Date',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[5],
      key: PROJECT_TABLE_COLUMN_INDEX[5],
      width: '10%',
      sorter: true,
      sortDirections: ['descend', 'ascend', 'descend'],
      render: startDate => (
        <>{startDate && moment(startDate).format(GENERIC_MOMENT_DATE_FORMAT)}</>
      ),
    },
    {
      title: 'End Date',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[6],
      key: PROJECT_TABLE_COLUMN_INDEX[6],
      width: '10%',
      sorter: true,
      sortDirections: ['descend', 'ascend', 'descend'],
      render: endDate => (
        <>
          {endDate ? (
            moment(endDate).format(GENERIC_MOMENT_DATE_FORMAT)
          ) : (
            <MinusOutlined />
          )}
        </>
      ),
    },
    {
      title: 'Project State',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[7],
      key: PROJECT_TABLE_COLUMN_INDEX[7],
      sorter: true,
      sortDirections: ['descend', 'ascend', 'descend'],
      width: '10%',
    },
    {
      title: 'Action',
      dataIndex: PROJECT_TABLE_COLUMN_INDEX[8],
      key: PROJECT_TABLE_COLUMN_INDEX[8],
      width: '20%',
      render: (_action, record) => (
        <>
          <Space size="middle" direction="horizontal">
            <Tooltip title="Edit Project">
              <Button
                data-testid={TEST_IDS.INDIVIDUAL_EDIT_BUTTON}
                type="primary"
                ghost
                htmlType="submit"
                shape="circle"
                size="large"
                onClick={() => this.editModalHandler(record._id, record)}
                disabled={!this.checkIfUserIsAdminOrBU()}
              >
                <EditOutlined />
              </Button>
            </Tooltip>
            <Tooltip title="Change State">
              <Button
                data-testid={TEST_IDS.INDIVIDUAL_EDIT_STATE_BUTTON}
                type="primary"
                ghost
                htmlType="submit"
                shape="circle"
                size="large"
                onClick={() => this.changeStateModalHandler(record._id)}
                disabled={!this.checkIfUserIsAdminOrBU()}
              >
                <SwapOutlined />
              </Button>
            </Tooltip>
          </Space>
        </>
      ),
    },
  ];

  hasReviewManagerHandler = e => {
    this.setState({ hasReviewManager: e.target.checked });
  };

  /**
   * Modal for project form
   * @returns {Modal}
   */
  projectFormModalContent = () => {
    const {
      showProjectFormModal,
      isLoading,
      projectId,
      employeesList,
      buList,
    } = this.state;
    const {
      pristine,
      submitting,
      invalid,
      updateField,
      projectStoreData: {
        projectName,
        reviewManager,
        reportingManager,
        buHead,
        startDate,
        invalidDateSelected,
      },
    } = this.props;

    const reduxFormConditions = pristine || submitting || invalid;

    const isReviewManagerAdded =
      (this.state.hasReviewManager && !!reviewManager) ||
      !this.state.hasReviewManager;

    const hasBUHead = !buHead || projectName.length === 0 || !startDate;
    const invalidFormDataLength = hasBUHead || !reportingManager;
    const invalidReportingFormData =
      invalidFormDataLength || !isReviewManagerAdded || invalidDateSelected;
    const performingAction =
      reduxFormConditions || isLoading || invalidReportingFormData;

    const cancelDisabled = submitting || isLoading;

    const columns = [
      {
        title: 'Resource Name',
        dataIndex: 'name',
        key: 'name',
        width: '20%',
        render: (_, record) => `${record.firstName} ${record.lastName}`,
      },
      {
        title: 'Designation',
        dataIndex: 'empRole',
        key: 'empRole',
        width: '20%',
        render: empRole => empRole || <MinusOutlined />,
      },
    ];

    const styleObj = {
      width: '85%',
      height: 'auto',
      margin: '0 auto',
      padding: '10px',
      position: 'relative',
      marginBottom: '20px',
    };

    const textStyleObj = {
      textAlign: 'center',
      fontSize: '18px',
    };

    return (
      <Modal
        title={projectId && <FormattedMessage {...messages.editModalHeader} />}
        maskClosable={false}
        wrapClassName="project-modal"
        visible={showProjectFormModal}
        onOk={this.updateProjectHandler}
        confirmLoading={isLoading}
        onCancel={() => {
          this.setState({ currentProjectData: [] });
          this.toggleModals();
        }}
        okButtonProps={{
          disabled: performingAction,
          'data-testid': TEST_IDS.ADD_PROJECT_SUBMIT_BUTTON,
        }}
        okText={projectId && <FormattedMessage {...messages.updateOKLabel} />}
        cancelButtonProps={{
          disabled: cancelDisabled,
          'data-testid': TEST_IDS.ADD_PROJECT_CANCEL_BUTTON,
        }}
      >
        <Form onSubmit={this.updateProjectHandler} name={PROJECT_LIST_FORM_KEY}>
          <Field
            label="Project Name"
            name="projectName"
            component={AInput}
            placeholder="Project Name"
            onChange={e => updateField(e.target.name, e.target.value)}
            required
            hasFeedback
          />
          {projectId && (
            <>
              <div style={textStyleObj}>
                {`The list of resources displayed here are for Quarter ${
                  getCurrentQuarterYear().quarter
                }.`}
              </div>
              <div style={styleObj}>
                <Table
                  className="table-bold-header"
                  {...logsTableProps}
                  dataSource={this.state.currentProjectData}
                  columns={columns}
                  pagination={false}
                  scroll={{
                    y: '35vh',
                  }}
                />
              </div>
            </>
          )}
          <Field
            label="Business Unit"
            name="buHead"
            component={ASelect}
            options={buList}
            placeholder="Business Unit Head"
            filterOption={(input, option) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField(`buHead`, e)}
            required
            showSearch
            allowClear
            hasFeedback
          />
          <Field
            label="Reporting Manager"
            name="reportingManager"
            component={ASelect}
            options={employeesList}
            placeholder="Reporting Manager"
            filterOption={(input, option) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField(`reportingManager`, e)}
            required
            showSearch
            allowClear
            hasFeedback
          />
          <Form.Item wrapperCol={{ offset: 7 }}>
            <Checkbox
              data-testid={TEST_IDS.REVIEW_MANAGER_CHECKBOX}
              onChange={this.hasReviewManagerHandler}
              checked={this.state.hasReviewManager}
            >
              Does the project have a review manager?
            </Checkbox>
          </Form.Item>
          {this.state.hasReviewManager && (
            <Field
              data-testid={TEST_IDS.REVIEW_MANAGER_NAME}
              label="Review Manager"
              name="reviewManager"
              component={ASelect}
              options={employeesList}
              placeholder="Review Manager"
              filterOption={(input, option) =>
                option &&
                option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
              }
              onFocus={e => e.preventDefault()}
              onBlur={e => e.preventDefault()}
              onChange={e => updateField(`reviewManager`, e)}
              required
              showSearch
              allowClear
              hasFeedback
            />
          )}
          <Field
            label="Start Date"
            name="startDate"
            component={ADatePicker}
            placeholder="Start Date"
            data-testid={TEST_IDS.PROJECT_START_DATE}
            hasFeedback
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('startDate', e)}
            required
            allowClear
          />
          <Field
            label="End Date"
            name="endDate"
            component={ADatePicker}
            data-testid={TEST_IDS.PROJECT_END_DATE}
            placeholder="End Date"
            hasFeedback
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField('endDate', e)}
            disabledDate={this.getDisabledDates}
            allowClear
          />
          <Divider orientation="left" style={{ margin: '24px 0 16px' }}>
            Integration Settings
          </Divider>
          <Form.Item label="GitHub Token">
            <Input.Password
              value={this.state.githubToken}
              onChange={e => this.setState({ githubToken: e.target.value })}
              placeholder="GitHub Token"
              style={{ marginBottom: 16 }}
              visibilityToggle
            />
          </Form.Item>

          <Form.Item label="SonarQube Token">
            <Input.Password
              value={this.state.sonarToken}
              onChange={e => this.setState({ sonarToken: e.target.value })}
              placeholder="SonarQube Token"
              style={{ marginBottom: 16 }}
              visibilityToggle // optional, true by default
            />
          </Form.Item>

          <Form.Item label="Organization">
            <Input
              value={this.state.organization}
              onChange={e => this.setState({ organization: e.target.value })}
              placeholder="Organization"
            />
          </Form.Item>

          <Divider orientation="left" style={{ margin: '24px 0 16px' }}>
            Emails
          </Divider>

          {this.state.emails.map((email, index) => (
            <div
              // eslint-disable-next-line react/no-array-index-key
              key={index}
              style={{
                display: 'flex',
                marginBottom: 16,
                alignItems: 'center',
              }}
            >
              <Input
                type="email"
                placeholder="Enter email address"
                value={email}
                onChange={e => {
                  const { value } = e.target;

                  this.setState(prevState => {
                    const newEmails = [...prevState.emails];
                    newEmails[index] = value;
                    return { emails: newEmails };
                  });
                }}
                style={{ flex: 1, marginRight: 8 }}
              />
              {index === 0 ? (
                <Button
                  type="primary"
                  icon={<PlusOutlined />}
                  onClick={() => {
                    const lastEmail = this.state.emails[
                      this.state.emails.length - 1
                    ];
                    if (lastEmail && lastEmail.trim() !== '') {
                      this.setState(prevState => ({
                        emails: [...prevState.emails, ''],
                      }));
                    }
                  }}
                />
              ) : (
                <Button
                  danger
                  icon={<MinusOutlined />}
                  onClick={() =>
                    this.setState(prevState => ({
                      emails: prevState.emails.filter((_, i) => i !== index),
                    }))
                  }
                />
              )}
            </div>
          ))}

          <Divider orientation="left" style={{ margin: '24px 0 16px' }}>
            Repositories
          </Divider>

          {this.state.repositories.map((repo, index) => (
            <div
              // eslint-disable-next-line react/no-array-index-key
              key={`repo-${index}`}
              style={{
                border: '1px solid #d9d9d9',
                padding: '16px',
                marginBottom: '16px',
                borderRadius: '4px',
              }}
            >
              <div
                style={{
                  display: 'flex',
                  justifyContent: 'space-between',
                  marginBottom: '16px',
                }}
              >
                <h4>Repository {index + 1}</h4>
                {index > 0 && (
                  <Button
                    danger
                    icon={<MinusOutlined />}
                    onClick={() => {
                      this.setState(prevState => {
                        if (prevState.repositories.length > 1) {
                          const newRepos = prevState.repositories.filter(
                            (_, i) => i !== index,
                          );
                          return { repositories: newRepos };
                        }
                        return {
                          repositories: [
                            {
                              repository: '',
                              branch: '',
                              sonarKey: '',
                              isSwaggerApplicable: false,
                              isSonarApplicable: false,
                              isCoverageApplicable: false,
                            },
                          ],
                        };
                      });
                    }}
                  />
                )}
              </div>

              <Form.Item label="Repository" required>
                <div style={{ width: '100%' }}>
                  <Input
                    placeholder="Repository"
                    value={repo.repository}
                    onChange={e =>
                      this.handleRepositoryChange(
                        index,
                        'repository',
                        e.target.value,
                      )
                    }
                    status={this.state.repositoryErrors[index] ? 'error' : ''}
                  />
                  {this.state.repositoryErrors[index] && (
                    <div
                      style={{
                        color: '#ff4d4f',
                        fontSize: '12px',
                        marginTop: '4px',
                      }}
                    >
                      {this.state.repositoryErrors[index]}
                    </div>
                  )}
                </div>
              </Form.Item>

              <Form.Item label="Branch">
                <Input
                  value={repo.branch}
                  onChange={e =>
                    this.handleRepositoryChange(index, 'branch', e.target.value)
                  }
                  placeholder="branch"
                />
              </Form.Item>

              <Form.Item label="Sonar Key">
                <Input
                  value={repo.sonarKey}
                  onChange={e =>
                    this.handleRepositoryChange(
                      index,
                      'sonarKey',
                      e.target.value,
                    )
                  }
                  placeholder="Sonar project key"
                />
              </Form.Item>

              <Form.Item>
                <Checkbox
                  checked={repo.isSwaggerApplicable}
                  onChange={e =>
                    this.handleRepositoryChange(
                      index,
                      'isSwaggerApplicable',
                      e.target.checked,
                    )
                  }
                >
                  Include Swagger
                </Checkbox>
              </Form.Item>

              <Form.Item>
                <Checkbox
                  checked={repo.isSonarApplicable}
                  onChange={e =>
                    this.handleRepositoryChange(
                      index,
                      'isSonarApplicable',
                      e.target.checked,
                    )
                  }
                >
                  Include Sonar
                </Checkbox>
              </Form.Item>

              <Form.Item>
                <Checkbox
                  checked={repo.isCoverageApplicable}
                  onChange={e =>
                    this.handleRepositoryChange(
                      index,
                      'isCoverageApplicable',
                      e.target.checked,
                    )
                  }
                >
                  Include Coverage
                </Checkbox>
              </Form.Item>

              {index === this.state.repositories.length - 1 && (
                <Button
                  type="dashed"
                  onClick={() => {
                    const lastRepo = this.state.repositories[
                      this.state.repositories.length - 1
                    ];
                    if (lastRepo.repository.trim()) {
                      this.setState(prevState => ({
                        repositories: [
                          ...prevState.repositories,
                          {
                            repository: '',
                            branch: '',
                            sonarKey: '',
                            isSwaggerApplicable: false,
                            isSonarApplicable: false,
                            isCoverageApplicable: false,
                          },
                        ],
                      }));
                    }
                  }}
                  disabled={!repo.repository.trim()}
                  block
                  icon={<PlusOutlined />}
                >
                  Add Repository
                </Button>
              )}
            </div>
          ))}
        </Form>
      </Modal>
    );
  };

  /**
   * Modal for change state form
   * @returns {Modal}
   */
  changeStateFormModalContent = () => {
    const { showChangeStateFormModal, isChangeStateFormLoading } = this.state;
    const {
      pristine,
      submitting,
      invalid,
      updateField,
      projectStoreData: { projectState },
    } = this.props;
    const reduxFormConditions = pristine || submitting || invalid;

    const projectStateAdded = projectState !== undefined && projectState !== '';
    const performingAction =
      reduxFormConditions || isChangeStateFormLoading || !projectStateAdded;

    const cancelDisabled = submitting || isChangeStateFormLoading;

    return (
      <Modal
        title={<FormattedMessage {...messages.changeStateModalHeader} />}
        maskClosable={false}
        visible={showChangeStateFormModal}
        onOk={this.changeProjectStateHandler}
        confirmLoading={isChangeStateFormLoading}
        onCancel={() => this.toggleChangeStateModal()}
        okButtonProps={{
          disabled: performingAction,
          'data-testid': TEST_IDS.CHANGE_PROJECT_STATE_SUBMIT_BUTTON,
        }}
        okText={<FormattedMessage {...messages.updateOKLabel} />}
        cancelButtonProps={{
          disabled: cancelDisabled,
          'data-testid': TEST_IDS.CHANGE_PROJECT_STATE_CANCEL_BUTTON,
        }}
      >
        <Form onSubmit={this.updateProjectHandler} name={PROJECT_LIST_FORM_KEY}>
          <Field
            label="Project State"
            name="projectState"
            component={ASelect}
            options={PROJECT_STATES}
            placeholder="Project State"
            filterOption={(input, option) =>
              option &&
              option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0
            }
            onFocus={e => e.preventDefault()}
            onBlur={e => e.preventDefault()}
            onChange={e => updateField(`projectState`, e)}
            required
            showSearch
            allowClear
            hasFeedback
          />
        </Form>
      </Modal>
    );
  };

  reset = () => {
    const { dispatch, updateField } = this.props;
    [
      'projectName',
      'reportingManager',
      'reviewManager',
      'buHead',
      'startDate',
      'endDate',
      'projectState',
      'githubToken',
      'sonarToken',
      'organization',
      'emails',
    ].forEach(key => {
      dispatch(change(PROJECT_LIST_FORM_KEY, key, ''));
      updateField(key, '');
    });
    this.setState({
      showProjectFormModal: false,
      showChangeStateFormModal: false,
      projectId: '',
      githubToken: '',
      sonarToken: '',
      organization: '',
      emails: [''],
    });
  };

  /**
   * TogglE Modal
   */
  toggleModals = () => {
    this.reset();
    const { showProjectFormModal, projectId } = this.state;
    this.setState({
      showProjectFormModal: !showProjectFormModal,
      projectId: showProjectFormModal ? '' : projectId,
      hasReviewManager: false,
    });
  };

  /**
   * Toggle Change State Modal
   */
  toggleChangeStateModal = () => {
    this.reset();
    const { showChangeStateFormModal, projectId } = this.state;
    this.setState({
      showChangeStateFormModal: !showChangeStateFormModal,
      projectId: showChangeStateFormModal ? '' : projectId,
    });
  };

  validateRepositories = () => {
    const { repositories } = this.state;
    const errors = {};
    let isValid = true;

    repositories.forEach((repo, index) => {
      if (!repo.repository || repo.repository.trim() === '') {
        errors[index] = 'Repository URL is required';
        isValid = false;
      }
    });

    this.setState({ repositoryErrors: errors });
    return isValid;
  };

  handleRepositoryChange = (index, field, value) => {
    this.setState(prevState => {
      const newRepos = [...prevState.repositories];
      newRepos[index] = {
        ...newRepos[index],
        [field]: value,
      };

      // Clear error for this field when user types
      const newErrors = { ...prevState.repositoryErrors };
      if (field === 'repository' && value.trim() !== '') {
        delete newErrors[index];
      }

      return {
        repositories: newRepos,
        repositoryErrors: newErrors,
      };
    });
  };

  updateProjectHandler = () => {
    const { projectStoreData, reset } = this.props;
    const {
      showProjectFormModal,
      projectId,
      githubToken,
      sonarToken,
      organization,
      emails,
      repositories,
    } = this.state;

    // Validate repositories before proceeding
    if (!this.validateRepositories()) {
      // Don't proceed if validation fails
      return;
    }

    // Filter out empty emails
    const validEmails = emails
      .map(email => email.trim())
      .filter(email => email !== '');

    // Map repositories with trimmed values
    const validRepositories = repositories.map(repo => ({
      ...repo,
      repository: repo.repository?.trim(),
      branch: repo.branch?.trim(),
      sonarKey: repo.sonarKey?.trim(),
    }));

    this.setState(
      {
        isLoading: true,
      },
      () => {
        const isUpdate = !!projectId;
        const endDate = projectStoreData.endDate
          ? moment(projectStoreData.endDate).format(GENERIC_MOMENT_DATE_FORMAT)
          : null;
        const body = {
          endDate,
          projectName: projectStoreData.projectName,
          pmUser: projectStoreData.reportingManager,
          businessUnitId: projectStoreData.buHead,
          reviewManager: this.state.hasReviewManager
            ? projectStoreData.reviewManager
            : null,
          startDate: moment(projectStoreData.startDate).format(
            GENERIC_MOMENT_DATE_FORMAT,
          ),
          hasReviewManager: this.state.hasReviewManager,
          // Integration settings
          githubToken: githubToken || undefined,
          sonarToken: sonarToken || undefined,
          organization: organization || undefined,
          emails: validEmails,
          repositories: validRepositories,
        };
        if (isUpdate) {
          body.id = projectId;
        }

        const payload = {
          method: isUpdate && 'PUT',
          body,
        };
        const URL = API_ENDPOINTS.UPDATE_PROJECT;

        request(URL, payload)
          .then(res => {
            this.setState(
              {
                isLoading: false,
                showProjectFormModal: !showProjectFormModal,
                projectId: '',
              },
              () => {
                this.getProjectList();
              },
            );
            notification.success({
              description: res.message,
            });
            reset();
          })
          .catch(error => {
            error.response.json().then(err =>
              notification.error({
                description: err.message,
              }),
            );
            this.setState({
              isLoading: false,
            });
          });
      },
    );
  };

  changeProjectStateHandler = () => {
    const { projectStoreData, reset } = this.props;
    const { showChangeStateFormModal, projectId } = this.state;
    this.setState(
      {
        isChangeStateFormLoading: true,
      },
      () => {
        const body = {
          projectState: projectStoreData.projectState,
          id: projectId,
        };

        const payload = {
          method: 'PATCH',
          body,
        };
        const URL = API_ENDPOINTS.UPDATE_PROJECT_STATE;

        request(URL, payload)
          .then(res => {
            this.setState(
              {
                isChangeStateFormLoading: false,
                showChangeStateFormModal: !showChangeStateFormModal,
                projectId: '',
              },
              () => {
                this.getProjectList();
              },
            );
            notification.success({
              description: res.message,
            });
            reset();
          })
          .catch(error => {
            error.response.json().then(err =>
              notification.error({
                description: err.message,
              }),
            );
            this.setState({
              isChangeStateFormLoading: false,
            });
          });
      },
    );
  };

  /**
   *
   * @param {*} projectId
   */
  editModalHandler = (projectId, currentProjectData) => {
    this.setState({ currentProjectData: currentProjectData.team });
    const { projectList } = this.state;
    const { updateField, dispatch } = this.props;
    const project = projectList.find(item => item._id === projectId);
    const startDate = moment(
      project[PROJECT_TABLE_COLUMN_INDEX[5]],
      GENERIC_MOMENT_DATE_FORMAT,
    );

    if (project) {
      const storeData = {
        projectId: project[PROJECT_TABLE_COLUMN_INDEX[0]],
        projectName: project[PROJECT_TABLE_COLUMN_INDEX[1]],
        reportingManager: project[PROJECT_TABLE_COLUMN_INDEX[3]]?._id || '',
        reviewManager: project[PROJECT_TABLE_COLUMN_INDEX[4]]?._id || '',
        buHead: project[PROJECT_TABLE_COLUMN_INDEX[9]]?._id || '',
        startDate: project[PROJECT_TABLE_COLUMN_INDEX[5]] ? startDate : '',
        endDate: project[PROJECT_TABLE_COLUMN_INDEX[6]]
          ? moment(
              project[PROJECT_TABLE_COLUMN_INDEX[6]],
              GENERIC_MOMENT_DATE_FORMAT,
            )
          : null,
      };

      // Update form fields
      Object.keys(storeData).forEach(key => {
        if (
          [
            'githubToken',
            'sonarToken',
            'organization',
            'emails',
            'repositories',
          ].includes(key)
        ) {
          // These are handled by local state
          return;
        }

        dispatch(change(PROJECT_LIST_FORM_KEY, key, storeData[key]));
        updateField(key, storeData[key]);
      });

      // Initialize repositories with project data or default empty array
      const repositories =
        project.configuration.repositories &&
        project.configuration.repositories.length > 0
          ? project.configuration.repositories
          : [
              {
                repository: '',
                branch: '',
                sonarKey: '',
                isSwaggerApplicable: false,
                isSonarApplicable: false,
                isCoverageApplicable: false,
              },
            ];

      this.setState({
        showProjectFormModal: true,
        hasReviewManager: !!storeData.reviewManager,
        projectId,
        githubToken: project.configuration.githubToken || '',
        sonarToken: project.configuration.sonarToken || '',
        organization: project.configuration.organization || '',
        emails:
          project.configuration.email && project.configuration.email.length > 0
            ? project.configuration.email
            : [''],
        repositories,
      });
    }
  };

  /**
   *
   * @param {*} projectId
   */
  changeStateModalHandler = projectId => {
    const { projectList } = this.state;
    const { updateField, dispatch } = this.props;
    const project = projectList.find(item => item._id === projectId);
    const { projectState } = project;

    dispatch(change(PROJECT_LIST_FORM_KEY, 'projectState', projectState));
    updateField('projectState', projectState);

    this.setState({
      showChangeStateFormModal: true,
      projectId,
    });
  };

  /**
   * Table sort, pagination change
   * @param {*} pagination
   * @param {*} _filters
   * @param {*} sorter
   */
  onTableOptionChange = (pagination, _filters, sorter) => {
    setDeepLinkURL({
      currentPage: pagination.current,
      searchText: this.state.searchValue,
    });
    this.getProjectList({
      pagination,
      sortType: GET_SORT_ORDER(sorter.order, SORTING.ASC),
      sortBy: sorter.field,
    });
  };

  delayedSearchResults = debounce(searchValue => {
    this.getProjectList({ searchValue });
    setDeepLinkURL({
      currentPage: this.state.pagination.current,
      searchText: searchValue,
    });
  }, 1000);

  handleSearchProject = e => {
    const searchText = e.target.value;
    this.setState(
      prevState => ({
        searchValue: searchText,
        pagination: {
          ...prevState.pagination,
          current: DEFAULT_PAGE,
        },
      }),
      () => {
        this.delayedSearchResults(searchText);
      },
    );
  };

  render() {
    const {
      isPaginationLoading,
      projectList,
      pagination,
      showChangeStateFormModal,
      showProjectFormModal,
      searchValue,
    } = this.state;

    return (
      <StyledProjectListingContainer>
        <Helmet>
          <title>Projects</title>
          <meta name="description" content="Project List" />
        </Helmet>
        <PageHeader
          title={<FormattedMessage {...messages.header} />}
          className="site-page-header"
          // eslint-disable-next-line no-sparse-arrays
          extra={[
            <Input
              prefix={<SearchOutlined />}
              key="Employee Search"
              placeholder="Search For Project"
              allowClear
              value={searchValue}
              style={{ width: 'inherit' }}
              onChange={this.handleSearchProject}
              data-testid={TEST_IDS.SEARCH_PROJECT}
            />,
          ]}
        >
          <div>
            <Table
              {...logsTableProps}
              className="table-bold-header"
              rowKey={record => record._id}
              pagination={pagination}
              loading={isPaginationLoading}
              columns={this.getColumnProps(this)}
              dataSource={projectList}
              onChange={this.onTableOptionChange}
            />
          </div>
        </PageHeader>
        {showProjectFormModal && this.projectFormModalContent()}
        {showChangeStateFormModal && this.changeStateFormModalContent()}
      </StyledProjectListingContainer>
    );
  }
}

ProjectListing.propTypes = {
  projectId: PropTypes.string,
  history: PropTypes.object,

  // Redux-form
  dispatch: PropTypes.func.isRequired,
  reset: PropTypes.func.isRequired,
  pristine: PropTypes.bool,
  submitting: PropTypes.bool,
  invalid: PropTypes.bool,
  formState: PropTypes.object,
  // Action
  updateField: PropTypes.func.isRequired,
  // Store
  projectStoreData: PropTypes.object,
};

const withReducer = useInjectReducer({
  key: PROJECT_LIST_FORM_KEY,
  reducer,
});

const mapStateToProps = state => {
  const getStateValues = createStructuredSelector({
    projectStoreData: makeSelectConnectedJiraList(),
  });
  return {
    formState: state.form,
    ...getStateValues(state),
  };
};

export const mapDispatchToProps = dispatch => ({
  updateField: (key, value) => dispatch(actions.updateField(key, value)),
});

const withConnect = connect(
  mapStateToProps,
  mapDispatchToProps,
);

export default compose(
  withRouter,
  withReducer,
  withConnect,
  reduxForm({
    form: PROJECT_LIST_FORM_KEY,
    fields: [
      'projectName',
      'reportingManager',
      'reviewManager',
      'buHead',
      'startDate',
      'endDate',
      'duplicates',
      'projectState',
    ],
    validate: formValidations.createValidator({
      projectName: [formValidations.required, isValidLength],
    }),
  }),
)(ProjectListing);
