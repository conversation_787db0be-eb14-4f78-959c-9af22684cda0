/*
 *
 * ProjectListing constants
 *
 */

// redux dispatch actions
export const UPDATE_FIELD = 'app/ProjectListing/UPDATE_FIELD';

// redux form name
export const PROJECT_LIST_FORM_KEY = 'ProjectListing';

// component constants
export const PROJECT_NAME_MAX_LENGTH = 100;
export const PROJECT_NAME_MIN_LENGTH = 2;
export const VALIDATION_MESSAGES = {
  MIN_LENGTH: `Project Name should be of minimum ${PROJECT_NAME_MIN_LENGTH} characters.`,
  MAX_LENGTH: `Project Name should be of maximum ${PROJECT_NAME_MAX_LENGTH} characters.`,
};
export const MAXIMUM_FILE_UPLOADS = 2;
export const PROJECT_TABLE_COLUMN_INDEX = [
  '_id',
  'projectName',
  'team',
  'reportingManager',
  'reviewManager',
  'startDate',
  'endDate',
  'projectState',
  'action',
  'businessUnitId',
];

export const PROJECT_STATES = [
  {
    label: 'Active',
    value: 'Active',
  },
  {
    label: 'Archived',
    value: 'Archived',
  },
  {
    label: 'Completed',
    value: 'Completed',
  },
  {
    label: 'Hold',
    value: 'Hold',
  },
];

/**
 * Length Validation of String,Min Max Length of value
 * @param(value)
 */
export const isValidLength = value => {
  if (value && value.length > 0 && value.length < PROJECT_NAME_MIN_LENGTH) {
    return VALIDATION_MESSAGES.MIN_LENGTH;
  }
  if (value && value.length > 0 && value.length > PROJECT_NAME_MAX_LENGTH) {
    return VALIDATION_MESSAGES.MAX_LENGTH;
  }
  return '';
};
