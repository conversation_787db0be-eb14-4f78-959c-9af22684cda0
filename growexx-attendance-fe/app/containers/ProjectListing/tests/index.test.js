/**
 *
 * Tests for ProjectListing
 *
 */

import React from 'react';
import { fireEvent, render, wait, waitForElement } from 'react-testing-library';
import { IntlProvider } from 'react-intl';
import { Provider } from 'react-redux';
import history from 'utils/history';
import { browserHistory } from 'react-router-dom';
import userEvent from '@testing-library/user-event';
import moment from 'moment';
import { ConnectedRouter } from 'connected-react-router';
import request from 'utils/request';
import { getUserData } from 'utils/Helper';
import ProjectListing from '../index';
import configureStore from '../../../configureStore';
import {
  failedResponse,
  responseWithList,
  TEST_IDS,
  failureWithPromise,
  responseWithUsersList,
  responseWithZeroList,
  stubSearchValue,
  generateBUData,
} from '../stub/test.stub';
import messages from '../messages';
import { isValidLength, VALIDATION_MESSAGES } from '../constants';
import { GENERIC_MOMENT_DATE_FORMAT, ROLES } from '../../constants';

jest.mock('utils/request');
jest.mock('utils/Helper');
let store;

const fieldUpdateViaPlaceHolder = [
  {
    key: 'Project Name',
    value: 'Stub',
  },
  {
    key: 'Start Date',
    value: '2022-01-01',
  },
  {
    key: 'End Date',
    value: '2022-12-12',
  },
];

const componentWrapper = () =>
  render(
    <Provider store={store}>
      <IntlProvider locale="en">
        <ConnectedRouter history={history}>
          <ProjectListing />
        </ConnectedRouter>
      </IntlProvider>
    </Provider>,
  );

describe('<ProjectListing />', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.HR,
    }));
  });

  beforeEach(() => {
    request.mockImplementation(() => Promise.resolve({}));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Should render and match the snapshot', () => {
    const {
      container: { firstChild },
    } = componentWrapper();
    expect(firstChild).toMatchSnapshot();
  });
});

describe('Check listing of projects is rendering properly', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.BU,
    }));
  });

  afterEach(() => {
    request.mockReset();
  });
  it('No Records found for employees', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithZeroList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()));
    const { getByText } = componentWrapper();
    await waitForElement(() => getByText(/No Data/i));
    expect(getByText(/No Data/i)).toBeTruthy();
  });
  it('projects with few records should be shown', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()));
    const { getByText } = componentWrapper();

    await wait(() => expect(request).toHaveBeenCalledTimes(3));
    expect(getByText('GAS')).toBeTruthy();
  });

  it('projects with pagination', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementation(() => Promise.resolve(responseWithList()));
    const { getByText, getByTitle } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));
    expect(getByText('GAS')).toBeTruthy();
    fireEvent.click(getByTitle('2'));
  });

  it('projects with sorting', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementation(() => Promise.resolve(responseWithList()));
    const { getByText } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));
    expect(getByText('Project Name')).toBeTruthy();
    fireEvent.click(getByText('Project Name'));
    fireEvent.click(getByText('Project Name'));
    fireEvent.click(getByText('Project Name'));
  });

  it('Failed Project api, employee list api, project managers list api', async () => {
    request
      .mockImplementationOnce(() => Promise.reject(failedResponse))
      .mockImplementationOnce(() => Promise.reject(failureWithPromise))
      .mockImplementationOnce(() => Promise.reject(failureWithPromise));
    const { getByText } = componentWrapper();
    await waitForElement(() => getByText(/No Data/i));
    expect(getByText(/No Data/i)).toBeTruthy();
  });
});

describe('Update Project Information', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.HR,
    }));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Update project with success', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementation(() =>
        Promise.resolve({
          status: 1,
        }),
      );

    const {
      getByTestId,
      getByText,
      getByPlaceholderText,
      getAllByRole,
    } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_BUTTON));
    // Fill all required fields
    fireEvent.change(getByPlaceholderText('Project Name'), {
      target: { value: 'Testing Project' },
    });
    fireEvent.change(getByPlaceholderText('Start Date'), {
      target: { value: '2022-01-01' },
    });
    fireEvent.change(getByPlaceholderText('End Date'), {
      target: { value: '2022-12-12' },
    });

    fireEvent.blur(getAllByRole('combobox')[0]);
    fireEvent.focus(getAllByRole('combobox')[0]);
    fireEvent.mouseDown(getAllByRole('combobox')[0]);
    fireEvent.change(getAllByRole('combobox')[0], {
      target: {
        value: 'Web',
      },
    });
    fireEvent.click(
      document.querySelectorAll('.ant-select-item-option-content')[0],
    );

    fireEvent.blur(getAllByRole('combobox')[1]);
    fireEvent.focus(getAllByRole('combobox')[1]);
    fireEvent.mouseDown(getAllByRole('combobox')[1]);
    fireEvent.change(getAllByRole('combobox')[1], {
      target: {
        value: 'Jane Smith',
      },
    });
    fireEvent.click(
      document.querySelectorAll('.ant-select-item-option-content')[1],
    );

    // Check Elements are showing
    expect(getByText('Update')).toBeTruthy();
    fireEvent.click(getByTestId(TEST_IDS.ADD_PROJECT_SUBMIT_BUTTON));
    expect(request).toHaveBeenCalledTimes(3);
  });

  it('Update project with review manager with success', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementation(() => Promise.resolve(responseWithUsersList()))
      .mockImplementation(() =>
        Promise.resolve({
          status: 1,
        }),
      );

    const { getByTestId, getByText } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_BUTTON));

    const reviewManagerCheckbox = getByTestId(TEST_IDS.REVIEW_MANAGER_CHECKBOX);
    fireEvent.click(reviewManagerCheckbox, {
      target: { checked: true },
    });

    // Select project start date
    const projectStartDate = getByTestId(TEST_IDS.PROJECT_START_DATE);
    fireEvent.focus(projectStartDate);
    fireEvent.blur(projectStartDate);
    fireEvent.mouseDown(projectStartDate);
    fireEvent.change(projectStartDate, {
      target: {
        value: moment()
          .startOf('month')
          .format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });

    // Select project end date
    const projectEndDate = getByTestId(TEST_IDS.PROJECT_END_DATE);
    fireEvent.focus(projectEndDate);
    fireEvent.blur(projectEndDate);
    fireEvent.mouseDown(projectEndDate);
    fireEvent.change(projectEndDate, {
      target: {
        value: moment()
          .startOf('month')
          .format(GENERIC_MOMENT_DATE_FORMAT),
      },
    });
    // Check Elements are showing
    expect(getByText('Update')).toBeTruthy();
    fireEvent.click(getByTestId(TEST_IDS.ADD_PROJECT_SUBMIT_BUTTON));
    expect(request).toHaveBeenCalledTimes(3);
  });

  it('Update project with cancel', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()));

    const { getByTestId, getByText } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_BUTTON));

    // Check Elements are showing
    expect(getByText('Update')).toBeTruthy();
    fireEvent.click(getByTestId(TEST_IDS.ADD_PROJECT_CANCEL_BUTTON));
  });

  it('Project Update failure', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.reject(failureWithPromise));

    const {
      getByTestId,
      getByText,
      findByPlaceholderText,
    } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_BUTTON));

    // Wait for modal/input to appear before interacting
    await Promise.all(
      fieldUpdateViaPlaceHolder.map(async d => {
        const input = await findByPlaceholderText(d.key);
        fireEvent.change(input, {
          target: { value: d.value },
        });
      }),
    );

    // Check Elements are showing
    expect(getByText('Update')).toBeTruthy();
    fireEvent.click(getByTestId(TEST_IDS.ADD_PROJECT_SUBMIT_BUTTON));
  });
});

describe('swap Project status', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.HR,
    }));
  });

  beforeEach(() => {
    request.mockImplementation(() => Promise.resolve(responseWithList()));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('swap project with success', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve({ status: 1 }));

    const { getByTestId, getByText, getByRole } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_STATE_BUTTON));

    // Check Elements are showing
    expect(
      getByText(messages.changeStateModalHeader.defaultMessage),
    ).toBeTruthy();
    expect(
      getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_SUBMIT_BUTTON),
    ).toBeTruthy();
    expect(
      getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_CANCEL_BUTTON),
    ).toBeTruthy();

    fireEvent.blur(getByRole('combobox'));
    fireEvent.focus(getByRole('combobox'));
    fireEvent.mouseDown(getByRole('combobox'));
    fireEvent.change(getByRole('combobox'), {
      target: {
        value: 'Active',
      },
    });
    fireEvent.click(
      document.querySelectorAll('.ant-select-item-option-content')[0],
    );

    fireEvent.click(getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_SUBMIT_BUTTON));
    expect(request).toHaveBeenCalledTimes(4);
  });

  it('swap project with cancel', async () => {
    request
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithUsersList()))
      .mockImplementationOnce(() => Promise.resolve({ status: 1 }));

    const { getByTestId, getByText } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.click(getByTestId(TEST_IDS.INDIVIDUAL_EDIT_STATE_BUTTON));

    // Check Elements are showing
    expect(
      getByText(messages.changeStateModalHeader.defaultMessage),
    ).toBeTruthy();
    expect(
      getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_SUBMIT_BUTTON),
    ).toBeTruthy();
    expect(
      getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_CANCEL_BUTTON),
    ).toBeTruthy();

    const projectState = getByText('Active');
    fireEvent.focus(projectState);
    fireEvent.blur(projectState);

    await wait(async () => {
      userEvent.click(projectState, undefined);
    });

    fireEvent.click(getByTestId(TEST_IDS.CHANGE_PROJECT_STATE_CANCEL_BUTTON));
    expect(request).toHaveBeenCalledTimes(3);
  });
});

describe('ProjectListing constants', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.HR,
    }));
  });

  beforeEach(() => {
    request.mockImplementation(() => Promise.resolve({}));
  });

  afterEach(() => {
    request.mockReset();
  });

  it('Should return appropriate constant validation messages', () => {
    expect(isValidLength('G')).toBe(VALIDATION_MESSAGES.MIN_LENGTH);
  });
  it('Should return appropriate constant validation messages', () => {
    expect(
      isValidLength(
        'projectNameStubMoreThan100CharactersToCheckTheValidation' +
          'projectNameStubMoreThan100CharactersToCheckTheValidation' +
          'projectNameStubMoreThan100CharactersToCheckTheValidation',
      ),
    ).toBe(VALIDATION_MESSAGES.MAX_LENGTH);
  });
});

describe('Search Project', () => {
  beforeAll(() => {
    store = configureStore({}, browserHistory);
    getUserData.mockImplementation(() => ({
      role: ROLES.USER,
    }));
  });

  beforeEach(() => {
    request.mockImplementation(() => Promise.resolve(responseWithList()));
  });

  afterEach(() => {
    request.mockReset();
  });
  it('Should call API with search value', async () => {
    delete window.location;
    window.location = { search: '?searchText=Test' };
    request
      .mockImplementationOnce(() => Promise.resolve(generateBUData()))
      .mockImplementationOnce(() => Promise.resolve(responseWithList()))
      .mockImplementationOnce(() => Promise.resolve({ status: 1 }))
      .mockImplementationOnce(() => Promise.resolve(responseWithList()));
    const { getByTestId } = componentWrapper();
    await wait(() => expect(request).toHaveBeenCalledTimes(3));

    // Fire Event
    fireEvent.change(getByTestId(TEST_IDS.SEARCH_PROJECT), stubSearchValue);
    await wait(() => expect(request).toHaveBeenCalledTimes(4));

    expect(request).toHaveBeenCalledTimes(4);
  });
});
