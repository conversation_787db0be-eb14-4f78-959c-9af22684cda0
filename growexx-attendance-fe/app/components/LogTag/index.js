/* eslint-disable indent */
import React, { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { Tag } from 'antd';
import { CloseOutlined, CheckOutlined, UndoOutlined } from '@ant-design/icons';
import { getUserData } from '../../utils/Helper';

const logStatusGray = 'rgba(240, 245, 255, 1)';
const logStatusGreen = 'rgb(173, 238, 184)';
const logStatusYellow = 'rgba(252, 227, 176, 1)';
const logStatusRed = 'rgba(247, 216, 217, 1)';

const LogTag = ({
  log,
  reviewDatePassed,
  handleLogClick,
  isShowRevokeToLog,
  changeLogStatus,
}) => {
  const [isMore, setIsMore] = useState(false);
  const [isOverflowing, setIsOverflowing] = useState(false);
  const containerRef = useRef(null);

  useEffect(() => {
    checkOverflow();
    window.addEventListener('resize', checkOverflow);
    return () => {
      window.removeEventListener('resize', checkOverflow);
    };
  }, []);

  const checkOverflow = () => {
    if (containerRef.current) {
      const { scrollWidth, clientWidth } = containerRef.current;
      setIsOverflowing(scrollWidth > clientWidth); // Check if content is overflowing
    }
  };

  const getLogColor = status => {
    if (status === 0) {
      return logStatusYellow;
    }
    if (status === 1) {
      return logStatusGreen;
    }
    if (status === 2) {
      return logStatusRed;
    }
    return logStatusGray;
  };

  const showMore = e => {
    e.stopPropagation();
    setIsMore(prev => !prev);
  };

  return (
    <Tag
      style={{ backgroundColor: getLogColor(log.logStatus) }}
      className="logTagStyle"
      title={`${log.jiraProjectName}\n${log.ticket}\n${log.jiraTitle}\n${
        log.timeSpentHours
      } hours`}
      onClick={() => handleLogClick(log)}
    >
      <div
        style={{
          display: 'flex',
          justifyContent: 'space-between',
          gap: '4px',
        }}
      >
        <span className="u-text-truncate">{log.ticket}</span>
        <span style={{ display: 'flex', alignItems: 'center' }}>
          <b style={{ marginRight: '8px' }}>{log.timeSpentHours}h</b>
          <span style={{ display: 'flex', gap: '4px' }}>
            {/* Only show approve/reject if log does not belong to logged-in user */}
            {!reviewDatePassed &&
              log.logStatus === 0 &&
              log.userId !== getUserData()._id && (
                <CloseOutlined
                  className="rejectAction"
                  onClick={e => changeLogStatus(e, log, 2)}
                />
              )}
            {!reviewDatePassed &&
              log.logStatus === 0 &&
              log.userId !== getUserData()._id && (
                <CheckOutlined
                  className="approveAction"
                  onClick={e => changeLogStatus(e, log, 1)}
                />
              )}
            {isShowRevokeToLog && (
              <UndoOutlined
                className="revokeAction"
                onClick={e => changeLogStatus(e, log, 0)}
              />
            )}
          </span>
        </span>
      </div>
      <div style={{ display: 'flex', alignItems: 'center' }}>
        <div
          style={{
            overflow: 'hidden',
            whiteSpace: isMore ? 'break-spaces' : 'nowrap',
          }}
          ref={containerRef}
        >
          {log.jiraTitle}
          {isOverflowing && isMore && (
            <Expand isMore={isMore} showMore={showMore} />
          )}
        </div>
        {isOverflowing && !isMore && (
          <Expand
            isMore={isMore}
            showMore={showMore}
            isOverflowing={isOverflowing}
          />
        )}
      </div>
    </Tag>
  );
};

const Expand = ({ isMore, showMore }) => (
  <button
    type="button"
    style={{
      border: 'none',
      background: 'none',
      color: 'blue',
      textDecoration: 'underline',
      alignSelf: 'end',
      cursor: 'pointer',
      display: 'inline',
    }}
    onClick={e => showMore(e)}
  >
    {isMore ? '-less' : '+more'}
  </button>
);

Expand.propTypes = {
  isMore: PropTypes.bool,
  showMore: PropTypes.func,
};

LogTag.propTypes = {
  log: PropTypes.object,
  reviewDatePassed: PropTypes.bool,
  isShowRevokeToLog: PropTypes.bool,
  handleLogClick: PropTypes.func,
  changeLogStatus: PropTypes.func,
};

export default LogTag;
