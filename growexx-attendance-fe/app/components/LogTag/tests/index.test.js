import React from 'react';
import { render } from '@testing-library/react';
import { getUserData } from 'utils/Helper';
import LogTag from '../index';

// Mock getUserData
jest.mock('utils/Helper', () => ({
  getUserData: jest.fn(),
}));

describe('LogTag approve/reject icon visibility', () => {
  const baseLog = {
    _id: 'log1',
    userId: 'user123',
    logStatus: 0,
    jiraProjectName: 'ProjectX',
    ticket: 'AL-279',
    jiraTitle: 'Test Log',
    timeSpentHours: 8,
  };
  const defaultProps = {
    reviewDatePassed: false,
    handleLogClick: jest.fn(),
    isShowRevokeToLog: false,
    changeLogStatus: jest.fn(),
  };

  afterEach(() => {
    jest.clearAllMocks();
  });

  it('does NOT render approve/reject icons if log belongs to logged-in user', () => {
    getUserData.mockReturnValue({ _id: 'user123' });
    const { container } = render(<LogTag log={baseLog} {...defaultProps} />);
    // Approve and reject icons should not be in the document
    expect(container.querySelector('.approveAction')).toBeNull();
    expect(container.querySelector('.rejectAction')).toBeNull();
  });

  it('renders approve/reject icons if log does NOT belong to logged-in user', () => {
    getUserData.mockReturnValue({ _id: 'otherUser' });
    const { container } = render(<LogTag log={baseLog} {...defaultProps} />);
    // Approve and reject icons should be present
    expect(container.querySelector('.approveAction')).not.toBeNull();
    expect(container.querySelector('.rejectAction')).not.toBeNull();
  });
});
