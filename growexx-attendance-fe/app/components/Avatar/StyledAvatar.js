import styled, { createGlobalStyle } from 'styled-components';

export const AvatarWrapper = styled.div`
  .ant-btn-group > .ant-btn:first-child {
    display: none;
  }
  .ant-dropdown-trigger,
  .ant-dropdown-trigger:hover {
    border-radius: 50% !important;
    background-color: @primary-color;
    color: @white;
  }
`;

// Responsive dropdown menu for Avatar
export const AvatarDropdownGlobalStyle = createGlobalStyle`
  .ant-dropdown-menu {
    min-width: 160px;
    max-width: 98vw;
    right: 0 !important;
    left: auto !important;
    word-break: break-word;
    white-space: normal;
    box-sizing: border-box;
  }
  @media (max-width: 480px) {
    .ant-dropdown-menu {
      min-width: 100px;
      font-size: 13px;
    }
  }
`;
