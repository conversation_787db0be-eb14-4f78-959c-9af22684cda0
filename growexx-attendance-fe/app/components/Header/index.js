/* eslint-disable react/no-array-index-key */
/**
 * Avatar/index.js
 *
 * This is the Avatar Component File.
 */
import React from 'react';
import { MenuItems } from './Constants';
import { StyledAppHeader, AvatarWrapper } from './StyledAppHeader';
import Avatar from '../Avatar';
import { AvatarDropdownGlobalStyle } from '../Avatar/StyledAvatar';

const Header = () => (
  <>
    <AvatarDropdownGlobalStyle />
    <StyledAppHeader>
      <AvatarWrapper>
        <Avatar menu={MenuItems} />
      </AvatarWrapper>
    </StyledAppHeader>
  </>
);

export default Header;
