/* eslint-disable indent */
import React, { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { Skeleton } from 'antd';
import { formatToWholeNumber } from 'utils/numberFormatter';
import { StyledStatusCard } from './StyledStatusCard';

function StatusCard({ cardData, loading, selectedProjectName }) {
  const [statusData, setStatusData] = useState({});

  useEffect(() => {
    const statusWiseData = {};
    if (cardData.epicEffortsInformation) {
      cardData.epicEffortsInformation.forEach(status => {
        statusWiseData[status.status] = status;
      });
    }
    setStatusData(statusWiseData);
  }, [loading]);

  const getTotalDifference = difference => {
    const totalSalesEstimate = cardData.countInformation
      ? cardData.countInformation.totalSalesEstimate
      : 0;
    const salesEstimationThreshold = totalSalesEstimate / 10;

    return renderDataWithRAGColor(difference, salesEstimationThreshold);
  };

  const renderDataWithRAGColor = (deviation, threshold) => {
    const deviationNum = Number(deviation);
    const displayDeviation = formatToWholeNumber(deviationNum);

    if (deviationNum >= 0) {
      return (
        <th rowSpan="1" colSpan="1" className="total-diff num">
          <div className="green-deviation-bg">{displayDeviation}</div>
        </th>
      );
    }

    if (Math.abs(deviationNum) > threshold) {
      return (
        <th rowSpan="1" colSpan="1" className="total-diff num">
          <div className="red-deviation-bg">{displayDeviation}</div>
        </th>
      );
    }

    return (
      <th rowSpan="1" colSpan="1" className="total-diff num">
        <div className="amber-deviation-bg">{displayDeviation}</div>
      </th>
    );
  };

  if (loading) {
    return (
      <StyledStatusCard>
        <div className="health-cards-container">
          <div className="left-cards">
            <Skeleton.Button active className="skeleton" />
          </div>
          <div className="right-cards">
            <Skeleton.Button active className="skeleton" />
          </div>
        </div>
      </StyledStatusCard>
    );
  }

  return (
    <StyledStatusCard>
      <div className="health-cards-container">
        <div className="left-cards">
          <table className="health-table">
            <thead>
              <tr className="total-row">
                <th rowSpan="1" colSpan="1" className="project-name">
                  Total Hours
                </th>
                <th
                  rowSpan="1"
                  colSpan="1"
                  className="total-sales-estimate num"
                >
                  {cardData.countInformation
                    ? formatToWholeNumber(
                        cardData.countInformation.totalSalesEstimate,
                      )
                    : '0'}
                </th>
                <th rowSpan="1" colSpan="2" className="total-dev-spent num">
                  {cardData.countInformation
                    ? formatToWholeNumber(cardData.countInformation.totalSpent)
                    : '0'}
                </th>
                {cardData.countInformation
                  ? getTotalDifference(
                      cardData.countInformation.totalDifference,
                    )
                  : getTotalDifference(0)}
              </tr>
              <tr>
                <th rowSpan="2" colSpan="1" className="epic">
                  Epic
                </th>
                <th rowSpan="2" colSpan="1" className="sales-estimate">
                  Sales Estimate
                </th>
                <th rowSpan="1" colSpan="1" className="dev-spent num">
                  {cardData.countInformation
                    ? formatToWholeNumber(
                        cardData.countInformation.totalDevSpent,
                      )
                    : '0'}
                </th>
                <th rowSpan="1" colSpan="1" className="dev-spent num">
                  {cardData.countInformation
                    ? formatToWholeNumber(
                        cardData.countInformation.totalDevToBeSpent,
                      )
                    : '0'}
                </th>
                <th rowSpan="2" colSpan="1" className="difference">
                  Difference
                </th>
              </tr>
              <tr>
                <th rowSpan="1" colSpan="1" className="spent">
                  Spent
                </th>
                <th rowSpan="1" colSpan="1" className="to-be-spent">
                  To Be Spent
                </th>
              </tr>
            </thead>
            <tbody>
              <tr>
                <td className="status">Done</td>
                <td className="num">
                  {statusData.Done
                    ? formatToWholeNumber(statusData.Done.totalSalesEstimates)
                    : '0'}
                </td>
                <td className="num">
                  {statusData.Done
                    ? formatToWholeNumber(statusData.Done.totalLoggedEfforts)
                    : '0'}
                </td>
                <td className="num">
                  {statusData.Done
                    ? formatToWholeNumber(statusData.Done.toBeLoggedEfforts)
                    : '0'}
                </td>
                <td className="num">
                  {statusData.Done
                    ? formatToWholeNumber(statusData.Done.difference)
                    : '0'}
                </td>
              </tr>
              <tr>
                <td className="status">In Progress</td>
                <td className="num">
                  {statusData['In Progress']
                    ? formatToWholeNumber(
                        statusData['In Progress'].totalSalesEstimates,
                      )
                    : '0'}
                </td>
                <td className="num">
                  {statusData['In Progress']
                    ? formatToWholeNumber(
                        statusData['In Progress'].totalLoggedEfforts,
                      )
                    : '0'}
                </td>
                <td className="num">
                  {statusData['In Progress']
                    ? formatToWholeNumber(
                        statusData['In Progress'].toBeLoggedEfforts,
                      )
                    : '0'}
                </td>
                <td className="num">
                  {statusData['In Progress']
                    ? formatToWholeNumber(statusData['In Progress'].difference)
                    : '0'}
                </td>
              </tr>
              <tr>
                <td className="status">To Do</td>
                <td className="num">
                  {statusData['To Do']
                    ? formatToWholeNumber(
                        statusData['To Do'].totalSalesEstimates,
                      )
                    : '0'}
                </td>
                <td className="num">
                  {statusData['To Do']
                    ? formatToWholeNumber(
                        statusData['To Do'].totalLoggedEfforts,
                      )
                    : '0'}
                </td>
                <td className="todo-spent num">
                  {statusData['To Do']
                    ? formatToWholeNumber(
                        statusData['To Do'].totalSalesEstimates,
                      )
                    : '0'}
                </td>
                <td className="num">
                  {statusData['To Do']
                    ? formatToWholeNumber(statusData['To Do'].difference)
                    : '0'}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
        <div className="right-cards">
          {[
            cardData.countInformation &&
              cardData.countInformation.totalEpicCount > 0 && {
                label: 'Epic Count',
                value: formatToWholeNumber(
                  cardData.countInformation.totalEpicCount,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Epic`,
              },
            cardData.countInformation &&
              cardData.countInformation.totalStoryCount > 0 && {
                label: 'Story Count',
                value: formatToWholeNumber(
                  cardData.countInformation.totalStoryCount,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Story`,
              },
            cardData.countInformation &&
              cardData.countInformation.storiesWithoutEpics > 0 && {
                label: 'Stories Without Epic',
                value: formatToWholeNumber(
                  cardData.countInformation.storiesWithoutEpics,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Story AND "Epic Link" is EMPTY`,
              },
            cardData.countInformation &&
              cardData.countInformation.epicWithoutSE > 0 && {
                label: 'Epic Without Sales Estimate',
                value: formatToWholeNumber(
                  cardData.countInformation.epicWithoutSE,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Epic AND originalEstimate = 0`,
              },
            cardData.countInformation &&
              cardData.countInformation.bugsWithoutLinkedStories > 0 && {
                label: 'Bugs Without Linked Stories',
                value: formatToWholeNumber(
                  cardData.countInformation.bugsWithoutLinkedStories,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Bug AND issueLinkType is EMPTY`,
              },
            cardData.countInformation &&
              cardData.countInformation.storyWithOriginalEstimateGreaterThan40 >
                0 && {
                label: 'Stories Estimated Over 40 Hours',
                value: formatToWholeNumber(
                  cardData.countInformation
                    .storyWithOriginalEstimateGreaterThan40,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Story AND originalEstimate > 40h`,
              },
            cardData.countInformation &&
              cardData.countInformation.subtasksCount > 0 && {
                label: 'Subtasks with Original Estimates Over 8 Hours',
                value: formatToWholeNumber(
                  cardData.countInformation.subtasksCount,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND issuetype = Sub-task AND originalEstimate > 1d`,
              },
            cardData.countInformation &&
              cardData.countInformation.storiesWithExtremeSubtaskCounts.count >
                0 && {
                label: 'Stories/Bugs with > 20 subtasks or < 1 subtask',
                value: formatToWholeNumber(
                  cardData.countInformation.storiesWithExtremeSubtaskCounts
                    .count,
                ),
                url: `${
                  cardData.countInformation.jiraStoryUrl
                }/issues/?jql=project = "${selectedProjectName}" AND (type = Story OR type = Bug OR type = Task) AND Key IN (${
                  cardData.countInformation.storiesWithExtremeSubtaskCounts
                    .jiraStoryNos
                })`,
              },
          ]
            .filter(Boolean)
            .map((row, index) => (
              <div
                key={row.label}
                className={`right-row ${index % 2 === 0 ? 'bg-gray' : ''}`}
              >
                <div className="row-col-1">{row.label}</div>
                {row.url ? (
                  <a style={{ color: 'blue' }} href={row.url} target="_blank">
                    <span className="rag-element">{row.value}</span>
                  </a>
                ) : (
                  <div>{row.value}</div>
                )}
              </div>
            ))}
        </div>
      </div>
    </StyledStatusCard>
  );
}

export default StatusCard;

StatusCard.propTypes = {
  cardData: PropTypes.object,
  loading: PropTypes.bool,
  selectedProjectName: PropTypes.string,
};
