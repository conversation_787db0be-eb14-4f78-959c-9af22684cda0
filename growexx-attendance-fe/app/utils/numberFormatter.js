/**
 * Formats a number to a whole number by truncating decimals
 * @param {number|string} value - The value to format
 * @returns {string} Formatted whole number as string, or '0' if invalid
 */
export const formatToWholeNumber = value => {
  if (value === null || value === undefined || value === '') return '0';
  const num = Number(value);
  return Number.isFinite(num) ? Math.trunc(num).toString() : '0';
};

/**
 * Formats a number with a specific number of decimal places
 * @param {number|string} value - The value to format
 * @param {number} [decimals=0] - Number of decimal places (default: 0)
 * @returns {string} Formatted number as string, or '0' if invalid
 */
export const formatNumber = (value, decimals = 0) => {
  if (value === null || value === undefined || value === '') return '0';
  const num = Number(value);
  if (!Number.isFinite(num)) return '0';

  const factor = 10 ** decimals;
  const rounded = Math.trunc(num * factor) / factor;
  return rounded.toString();
};
