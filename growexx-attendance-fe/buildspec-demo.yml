version: 0.2
environment_variables:
  plaintext:
    S3_BUCKET: "demo.timesheet.growexx.com"
    CONFIG_BUCKET_NAME: "demo.timesheet.growexx.com-artifacts"
    DISTRIBUTION_ID: "E2900K4YSFJXNS"
    projectKey: "growexx-attendance-fe"
    projctVersion: "master"
    projectName: "growexx-attendance-fe"
env:
  parameter-store:
    SONAR_TOKEN: "SONAR_TOKEN"
    SONAR_HOST: "SONAR_HOST"
phases:
  install:
      runtime-versions:
       nodejs: 18
      commands:
      - echo Installing source NPM dependencies...
      - aws s3 cp s3://$CONFIG_BUCKET_NAME/config-ui/.env .
      - npm install --legacy-peer-deps
  build:
    commands:
      - curl ifconfig.co
      - npm run test  
      - wget -q https://binaries.sonarsource.com/Distribution/sonar-scanner-cli/sonar-scanner-cli-3.2.0.1227-linux.zip
      - unzip sonar-scanner-cli-3.2.0.1227-linux.zip
      - cp sonar-project.properties sonar-scanner-3.2.0.1227-linux/conf/sonar-scanner.properties
      - ./sonar-scanner-3.2.0.1227-linux/bin/sonar-scanner -Dsonar.host.url=$SONAR_HOST -Dsonar.login=$SONAR_TOKEN -Dsonar.projectKey=$projectKey -Dsonar.projectVersion=$projectVersion
      - echo Build started on `date`
      - npm run build
  post_build:
    commands:
      - pwd
      - aws s3 rm s3://${S3_BUCKET} --recursive --exclude "profile-picture/*" --exclude "logo/*"
      - aws s3 cp build s3://${S3_BUCKET} --recursive  --acl public-read
      - aws cloudfront create-invalidation --distribution-id ${DISTRIBUTION_ID} --paths "/*"
      - echo Build completed on `date`
cache:
  paths:
    - 'node_modules/**/*'
artifacts:
  files:
    - "**/*"
  base-directory: build
